{"README.html": {"href": "README.html", "title": "DrawnUi Documentation | DrawnUi Documentation", "summary": "DrawnUi Documentation This folder contains the DocFX-based documentation for DrawnUi. Building the Documentation Option 1: Using .NET Tool (Recommended) To build the documentation locally: Install DocFX as a .NET global tool: dotnet tool install -g docfx Navigate to the docs folder: cd docs Build the documentation: docfx build Preview the documentation: docfx serve _site Option 2: Using Docker If you don't have .NET installed, you can use Docker: # From the repository root docker run --rm -it -v ${PWD}:/app -w /app/docs mcr.microsoft.com/dotnet/sdk:7.0 bash -c \"dotnet tool install -g docfx && docfx build\" Option 3: Using NPM Package (Alternative) For environments where .NET isn't available: Install docfx via npm: npm install -g @tsgkadot/docfx-flavored-markdown Build the documentation: dfm build Documentation Structure /api/: Auto-generated API documentation from XML comments /articles/: Conceptual documentation articles and tutorials /images/: Images used in the documentation /templates/: DocFX templates for styling Contributing to the Documentation When contributing to the documentation: For API documentation, add XML comments to the code in the DrawnUi source files For conceptual documentation, edit or create Markdown files in the /articles/ folder After making changes, build the documentation to verify it renders correctly API Documentation Guidelines When adding XML comments to your code: Use the <summary> tag to provide a brief description of the class/method/property Use the <param> tag to document parameters Use the <returns> tag to document return values Use the <example> tag to provide usage examples Use <see cref=\"...\"/> to create links to other types/members"}, "api/index.html": {"href": "api/index.html", "title": "API Documentation | DrawnUi Documentation", "summary": "API Documentation This section contains the API documentation for DrawnUi, automatically generated from the code comments. Main Namespaces DrawnUi.Draw: Core drawing and rendering functionality DrawnUi.Controls: UI controls and components DrawnUi.Features: Additional features and capabilities Core Components The main components you'll interact with include: SkiaControl: Base class for all drawn controls SkiaShape: Basic shape rendering SkiaLabel: Text rendering with advanced formatting options SkiaLayout: Layout management for drawn elements SkiaButton, SkiaSwitch, SkiaCheckbox: Platform-styled UI controls"}, "articles/advanced/game-ui.html": {"href": "articles/advanced/game-ui.html", "title": "Building Game UIs and Interactive Games with DrawnUi.Maui | DrawnUi Documentation", "summary": "Building Game UIs and Interactive Games with DrawnUi.Maui DrawnUi.Maui is not just for business apps—it’s also a powerful platform for building interactive games and game-like UIs. With direct SkiaSharp rendering, real-time animation, and flexible input handling, you can create everything from simple arcade games to rich, animated dashboards. Why Use DrawnUi.Maui for Games? High-performance canvas rendering on all platforms Frame-based animation with SkiaSprite, SkiaGif, SkiaLottie, and custom logic Flexible input: tap, drag, swipe, and multi-touch Custom drawing: draw shapes, sprites, and effects directly Easy integration with other DrawnUi controls and layouts Game Loop and Real-Time Updates For interactive games, you need a game loop that updates the game state and redraws the UI at regular intervals. Example: Simple Game Loop public class GamePage : SkiaLayout { private bool _running; private Timer _timer; private int _playerX = 100; private int _playerY = 100; public GamePage() { // Start the game loop _running = true; _timer = new Timer(OnTick, null, 0, 16); // ~60 FPS } private void OnTick(object state) { if (!_running) return; // Update game state _playerX += 1; // Redraw Invalidate(); } protected override void OnDraw(SKCanvas canvas, SKRect destination, float scale) { base.OnDraw(canvas, destination, scale); // Draw player as a circle canvas.DrawCircle(_playerX, _playerY, 20, new SKPaint { Color = SKColors.Blue }); } protected override void OnDisposing() { _running = false; _timer?.Dispose(); base.OnDisposing(); } } Using SkiaSprite for Animated Characters SkiaSprite makes it easy to animate sprite sheets: <DrawUi:SkiaSprite x:Name=\"PlayerSprite\" Source=\"character_run.png\" Columns=\"8\" Rows=\"1\" FramesPerSecond=\"12\" AutoPlay=\"True\" WidthRequest=\"128\" HeightRequest=\"128\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> In code-behind, you can control animation state: PlayerSprite.Start(); // Start animation PlayerSprite.Stop(); // Stop animation PlayerSprite.CurrentFrame = 0; // Set frame Handling Input: Tap, Drag, and Gestures DrawnUi.Maui supports rich gesture handling for interactive games: <DrawUi:SkiaHotspot Tapped=\"OnPlayerTapped\"> <DrawUi:SkiaSprite ... /> </DrawUi:SkiaHotspot> In code-behind: private void OnPlayerTapped(object sender, EventArgs e) { // Respond to tap (e.g., jump, attack) } For drag or swipe, use gesture listeners or override touch methods in your control. Combining UI and Game Elements You can mix game elements with standard DrawnUi controls: <DrawUi:SkiaLayout LayoutType=\"Column\"> <DrawUi:SkiaLabel Text=\"Score: 123\" FontSize=\"24\" /> <DrawUi:SkiaSprite ... /> <DrawUi:SkiaButton Text=\"Pause\" Tapped=\"OnPause\" /> </DrawUi:SkiaLayout> Example: Simple Tap Game <DrawUi:SkiaLayout> <DrawUi:SkiaHotspot Tapped=\"OnTap\"> <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"100\" HeightRequest=\"100\" BackgroundColor=\"Red\" /> </DrawUi:SkiaHotspot> <DrawUi:SkiaLabel x:Name=\"ScoreLabel\" Text=\"Score: 0\" FontSize=\"24\" /> </DrawUi:SkiaLayout> private int _score = 0; private void OnTap(object sender, EventArgs e) { _score++; ScoreLabel.Text = $\"Score: {_score}\"; } Tips for Game UI Performance Use Cache=\"Operations\" or Cache=\"Image\" for static backgrounds or UI elements Minimize redraws: only call Invalidate() when needed Use SkiaLabelFps to monitor frame rate For complex games, manage game state and rendering in a dedicated class Advanced: Integrating Addons (Camera, Maps, Charts) Use DrawnUi.Maui.Camera for AR or camera-based games Overlay charts or live data with DrawnUi.Maui.LiveCharts Add maps or location-based features with DrawnUi.Maui.MapsUi Summary DrawnUi.Maui enables you to build interactive, animated, and performant game UIs on any platform. Combine sprites, custom drawing, and flexible input to create unique experiences—whether for games, dashboards, or playful business apps."}, "articles/advanced/gestures.html": {"href": "articles/advanced/gestures.html", "title": "Advanced Gesture Handling in DrawnUi.Maui | DrawnUi Documentation", "summary": "Advanced Gesture Handling in DrawnUi.Maui DrawnUi.Maui provides a robust and extensible gesture system for building interactive, touch-driven UIs. This article covers how to use built-in gestures, implement custom gesture logic, and best practices for advanced scenarios. Gesture System Overview Unified gesture model for tap, drag, swipe, pinch, long-press, and multi-touch ISkiaGestureListener interface for custom gesture handling SkiaHotspot and gesture listeners for declarative and code-based gestures Gesture locking and propagation control for complex UI hierarchies Basic Tap and Click Handling Use SkiaHotspot for simple tap/click detection: <DrawUi:SkiaHotspot Tapped=\"OnTapped\"> <DrawUi:SkiaShape Type=\"Circle\" BackgroundColor=\"Blue\" WidthRequest=\"80\" HeightRequest=\"80\" /> </DrawUi:SkiaHotspot> private void OnTapped(object sender, EventArgs e) { // Handle tap } Handling Drag, Swipe, and Multi-Touch Implement ISkiaGestureListener for advanced gestures: public class DraggableShape : SkiaShape, ISkiaGestureListener { private float _x, _y; public override void OnParentChanged() { base.OnParentChanged(); RegisterGestureListener(this); } public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result) { if (type == TouchActionType.Pan) { _x = args.Location.X; _y = args.Location.Y; Invalidate(); return true; } return false; } } Gesture Locking and Propagation Use the LockChildrenGestures property to control gesture propagation: LockTouch.Enabled: Prevents children from receiving gestures LockTouch.Disabled: Allows all gestures to propagate LockTouch.PassTap: Only tap events pass through LockTouch.PassTapAndLongPress: Tap and long-press pass through Example: <DrawUi:SkiaLayout LockChildrenGestures=\"PassTap\"> <!-- Only tap gestures reach children --> </DrawUi:SkiaLayout> Custom Gesture Handling in Code Override OnGestureEvent for fine-grained control: public override ISkiaGestureListener OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result, SKPoint childOffset, SKPoint childOffsetDirect) { // Custom logic for gesture routing or handling return base.OnGestureEvent(type, args, result, childOffset, childOffsetDirect); } Multi-Touch and Pinch-to-Zoom Listen for pinch and multi-touch events: public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result) { if (type == TouchActionType.Pinch) { // args.PinchScale, args.Center, etc. // Handle zoom return true; } return false; } Gesture Utilities and Best Practices Use HadInput to track which listeners have received input Use InputTransparent to make controls ignore gestures For performance, avoid deep gesture listener hierarchies Use debug logging to trace gesture flow Example: Swipe-to-Delete List Item <DrawUi:SkiaLayout ItemsSource=\"{Binding Items}\"> <DrawUi:SkiaLayout.ItemTemplate> <DataTemplate> <local:SwipeToDeleteItem /> </DataTemplate> </DrawUi:SkiaLayout.ItemTemplate> </DrawUi:SkiaLayout> public class SwipeToDeleteItem : SkiaLayout, ISkiaGestureListener { public override void OnParentChanged() { base.OnParentChanged(); RegisterGestureListener(this); } public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result) { if (type == TouchActionType.Pan && result == TouchActionResult.Panning) { // Move item horizontally this.TranslationX = args.Location.X; Invalidate(); return true; } if (type == TouchActionType.Pan && result == TouchActionResult.Up) { if (Math.Abs(this.TranslationX) > 100) { // Trigger delete // ... } this.TranslationX = 0; Invalidate(); return true; } return false; } } Debugging and Extending Gestures Use debug output to trace gesture events and propagation Extend or compose gesture listeners for complex scenarios Integrate with platform-specific gesture APIs if needed Summary DrawnUi.Maui’s gesture system enables rich, interactive UIs with tap, drag, swipe, pinch, and custom gestures. Use SkiaHotspot for simple cases, ISkiaGestureListener for advanced logic, and gesture locking for complex layouts."}, "articles/advanced/gradients.html": {"href": "articles/advanced/gradients.html", "title": "Using Gradients in DrawnUi.Maui | DrawnUi Documentation", "summary": "Using Gradients in DrawnUi.Maui DrawnUi.<PERSON><PERSON> provides powerful gradient support for shapes, text, and images, enabling visually rich and modern UI designs. This article covers the types of gradients available, how to apply them, and practical examples for common scenarios. Gradient Types DrawnUi.Maui supports several gradient types: Linear Gradient: Colors transition along a straight line. Radial Gradient: Colors radiate outward from a center point. Sweep Gradient: Colors sweep around a center point in a circular fashion. Applying Gradients to Shapes You can apply gradients to the background or stroke of any SkiaShape using the BackgroundGradient and StrokeGradient properties. Linear Gradient Example <DrawUi:SkiaShape Type=\"Rectangle\" CornerRadius=\"16\" WidthRequest=\"200\" HeightRequest=\"100\"> <DrawUi:SkiaShape.BackgroundGradient> <DrawUi:SkiaGradient Type=\"Linear\" StartColor=\"#FF6A00\" EndColor=\"#FFD800\" StartPoint=\"0,0\" EndPoint=\"1,1\" /> </DrawUi:SkiaShape.BackgroundGradient> </DrawUi:SkiaShape> Radial Gradient Example <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"120\" HeightRequest=\"120\"> <DrawUi:SkiaShape.BackgroundGradient> <DrawUi:SkiaGradient Type=\"Radial\" StartColor=\"#00C3FF\" EndColor=\"#FFFF1C\" Center=\"0.5,0.5\" Radius=\"0.5\" /> </DrawUi:SkiaShape.BackgroundGradient> </DrawUi:SkiaShape> Sweep Gradient Example <DrawUi:SkiaShape Type=\"Ellipse\" WidthRequest=\"180\" HeightRequest=\"100\"> <DrawUi:SkiaShape.BackgroundGradient> <DrawUi:SkiaGradient Type=\"Sweep\" StartColor=\"#FF0080\" EndColor=\"#7928CA\" Center=\"0.5,0.5\" /> </DrawUi:SkiaShape.BackgroundGradient> </DrawUi:SkiaShape> Multi-Stop Gradients You can define gradients with multiple color stops: <DrawUi:SkiaShape Type=\"Rectangle\" WidthRequest=\"220\" HeightRequest=\"60\"> <DrawUi:SkiaShape.BackgroundGradient> <DrawUi:SkiaGradient Type=\"Linear\" StartPoint=\"0,0\" EndPoint=\"1,0\"> <DrawUi:SkiaGradient.Stops> <DrawUi:GradientStop Color=\"#FF6A00\" Offset=\"0.0\" /> <DrawUi:GradientStop Color=\"#FFD800\" Offset=\"0.5\" /> <DrawUi:GradientStop Color=\"#00FFB4\" Offset=\"1.0\" /> </DrawUi:SkiaGradient.Stops> </DrawUi:SkiaGradient> </DrawUi:SkiaShape.BackgroundGradient> </DrawUi:SkiaShape> Applying Gradients to Text You can apply gradients to text using the FillGradient property on SkiaLabel: <DrawUi:SkiaLabel Text=\"Gradient Text\" FontSize=\"32\" FillGradient=\"{StaticResource MyGradient}\" /> Or define inline: <DrawUi:SkiaLabel Text=\"Sunset\" FontSize=\"40\"> <DrawUi:SkiaLabel.FillGradient> <DrawUi:SkiaGradient Type=\"Linear\" StartColor=\"#FF6A00\" EndColor=\"#FFD800\" StartPoint=\"0,0\" EndPoint=\"1,0\" /> </DrawUi:SkiaLabel.FillGradient> </DrawUi:SkiaLabel> Applying Gradients to Images You can overlay gradients on images using the UseGradient, StartColor, and EndColor properties on SkiaImage: <DrawUi:SkiaImage Source=\"photo.jpg\" UseGradient=\"True\" StartColor=\"#00000000\" EndColor=\"#CC000000\" /> This creates a fade effect from transparent to black over the image. Defining Gradients as Resources For reuse, define gradients as resources: <ContentPage.Resources> <DrawUi:SkiaGradient x:Key=\"MyGradient\" Type=\"Linear\" StartColor=\"#FF6A00\" EndColor=\"#FFD800\" StartPoint=\"0,0\" EndPoint=\"1,1\" /> </ContentPage.Resources> Then reference with: <DrawUi:SkiaLabel Text=\"Reusable Gradient\" FillGradient=\"{StaticResource MyGradient}\" /> C# Example: Creating a Gradient in Code var gradient = new SkiaGradient { Type = SkiaGradientType.Linear, StartColor = Colors.Red, EndColor = Colors.Yellow, StartPoint = new Point(0, 0), EndPoint = new Point(1, 1) }; myShape.BackgroundGradient = gradient; Tips and Best Practices Use gradients to add depth and visual interest to your UI. For performance, prefer simple gradients or reuse gradient resources. Gradients can be animated by changing their properties in code. Combine gradients with shadows for modern card and button designs. Summary Gradients in DrawnUi.Maui are flexible and easy to use across shapes, text, and images. Use the provided properties and examples to create visually appealing, modern interfaces."}, "articles/advanced/layout-system.html": {"href": "articles/advanced/layout-system.html", "title": "Layout System Architecture | DrawnUi Documentation", "summary": "Layout System Architecture This article covers the internal architecture of DrawnUi.<PERSON><PERSON>'s layout system, designed for developers who want to understand how layouts work under the hood or extend the system with custom layout types. Layout System Overview DrawnUi.Maui's layout system is built on a core principle: direct rendering to canvas with optimizations for mobile and desktop platforms. Unlike traditional MAUI layouts that create native UI elements, DrawnUi.<PERSON>ui renders everything using SkiaSharp, enabling consistent cross-platform visuals and better performance for complex UIs. Core Components SkiaControl SkiaControl is the foundation of the entire UI system. It provides core capabilities for: Position tracking in the rendering tree Coordinate transformation for touch and rendering Efficient invalidation system Support for effects and transforms Hit testing and touch input handling Visibility management Its key methods include: OnMeasure: Determines the size requirements of the control OnArrange: Positions the control within its parent OnDraw: Renders the control using a SkiaSharp canvas InvalidateInternal: Manages rendering invalidation SkiaLayout SkiaLayout extends SkiaControl to provide layout functionality. It's implemented as a partial class with functionality split across files by layout type: SkiaLayout.cs: Core layout mechanisms SkiaLayout.Grid.cs: Grid layout implementation SkiaLayout.ColumnRow.cs: Stack-like layouts SkiaLayout.BuildWrapLayout.cs: Wrap layout implementation SkiaLayout.ListView.cs: Virtualized list rendering SkiaLayout.IList.cs: List-specific optimization SkiaLayout.ViewsAdapter.cs: Template management This approach allows specialized handling for each layout type while sharing common infrastructure. Layout Structures The system uses specialized structures to efficiently track and manage layout calculations: LayoutStructure: Tracks arranged controls in stack layouts GridStructure: Manages grid-specific layout information ControlInStack: Contains information about a control's position Advanced Concepts Virtualization Virtualization is a key performance optimization that only renders items currently visible in the viewport. This enables efficient rendering of large collections. The VirtualizationMode enum defines several strategies: None: All items are rendered Enabled: Only visible items are rendered and measured Smart: Renders visible items plus a buffer Managed: Uses a managed renderer for advanced cases Virtualization works alongside template recycling to minimize both CPU and memory usage. Template Recycling The RecyclingTemplate property determines how templates are reused across items: None: New instance created for each item Enabled: Templates are reused as items scroll out of view Smart: Reuses templates with additional optimizations The ViewsAdapter class manages template instantiation, recycling, and state management. Measurement Strategies The layout system supports different strategies for measuring item sizes: MeasureFirst: Measures all items before rendering MeasureAll: Continuously measures all items MeasureVisible: Only measures visible items These strategies let you balance between layout accuracy and performance. Extending the Layout System Creating a Custom Layout Type To create a custom layout type, you'll typically: Create a new class inheriting from SkiaLayout Override the OnMeasure and OnArrange methods Implement custom measurement and arrangement logic Optionally create custom properties for layout configuration Here's a simplified example of a circular layout implementation: public class CircularLayout : SkiaLayout { public static readonly BindableProperty RadiusProperty = BindableProperty.Create(nameof(Radius), typeof(float), typeof(CircularLayout), 100f, propertyChanged: (b, o, n) => ((CircularLayout)b).InvalidateMeasure()); public float Radius { get => (float)GetValue(RadiusProperty); set => SetValue(RadiusProperty, value); } protected override SKSize OnMeasure(float widthConstraint, float heightConstraint) { // Need enough space for a circle with our radius return new SKSize(Radius * 2, Radius * 2); } protected override void OnArrange(SKRect destination) { base.OnArrange(destination); // Skip if no children if (Children.Count == 0) return; // Calculate center point SKPoint center = new SKPoint(destination.MidX, destination.MidY); float angleStep = 360f / Children.Count; // Position each child around the circle for (int i = 0; i < Children.Count; i++) { var child = Children[i]; if (!child.IsVisible) continue; // Calculate position on circle float angle = i * angleStep * (float)Math.PI / 180f; float x = center.X + Radius * (float)Math.Cos(angle) - child.MeasuredSize.Width / 2; float y = center.Y + Radius * (float)Math.Sin(angle) - child.MeasuredSize.Height / 2; // Arrange child at calculated position child.Arrange(new SKRect(x, y, x + child.MeasuredSize.Width, y + child.MeasuredSize.Height)); } } } Best Practices for Custom Layouts Minimize Measure Calls: Measure operations are expensive. Cache results when possible. Implement Proper Invalidation: Ensure your layout properly invalidates when properties affecting layout change. Consider Virtualization: For layouts with many items, implement virtualization to only render visible content. Optimize Arrangement Logic: Keep arrangement logic simple and efficient, especially for layouts that update frequently. Respect Constraints: Always respect the width and height constraints passed to OnMeasure. Cache Layout Calculations: For complex layouts, consider caching calculations that don't need to be redone every frame. Extend SkiaLayout: Instead of creating entirely new layout types, consider extending SkiaLayout and creating a new LayoutType enum value if needed. Layout System Internals The Layout Process The layout process follows these steps: Parent Invalidates Layout: When a change requires remeasurement OnMeasure Called: Layout determines its size requirements Parent Determines Size: Parent decides actual size allocation OnArrange Called: Layout positions itself and its children OnDraw Called: Layout renders itself and its children Coordinate Spaces The layout system deals with multiple coordinate spaces: Local Space: Relative to the control itself (0,0 is top-left of control) Parent Space: Relative to the parent control Canvas Space: Relative to the drawing canvas Screen Space: Relative to the screen (used for touch input) The system provides methods for converting between these spaces, making it easier to handle positioning and hit testing. Layout-Specific Properties Layout controls have unique bindable properties that affect their behavior: ColumnDefinitions/RowDefinitions: Define grid structure Spacing: Controls space between items Padding: Controls space inside the layout edges LayoutType: Determines layout strategy ItemsSource/ItemTemplate: For data-driven layouts Performance Considerations Rendering Optimization The rendering system is optimized using several techniques: Clipping: Only renders content within visible bounds Caching: Different caching strategies for balancing performance Background Processing: Template initialization on background threads Incremental Loading: Loading and measuring items incrementally When to Use Each Layout Type Absolute: When precise positioning is needed (graphs, custom visualizations) Grid: For tabular data and form layouts Column/Row: For sequential content in one direction Wrap: For content that should flow naturally across lines (tags, flow layouts) Debugging Layouts For debugging layout issues, use these built-in features: Set IsDebugRenderBounds to true to visualize layout boundaries Use SkiaLabelFps to monitor rendering performance Add the DebugRenderGraph control to visualize the rendering tree Summary DrawnUi.Maui's layout system provides a powerful foundation for creating high-performance, visually consistent UIs across platforms. By understanding its architecture, you can leverage its capabilities to create custom layouts and optimize your application's performance."}, "articles/advanced/platform-styling.html": {"href": "articles/advanced/platform-styling.html", "title": "Platform-Specific Styling | DrawnUi Documentation", "summary": "Platform-Specific Styling DrawnUi controls support platform-specific styling to ensure your app looks and feels native on each platform. Using Platform Styles The ControlStyle Property Many DrawnUi controls include a ControlStyle property that determines their visual appearance: Unset: Default styling defined by the control Platform: Automatically selects the appropriate style for the current platform Cupertino: iOS-style appearance Material: Android Material Design appearance Windows: Windows-style appearance Basic Usage <!-- Automatically use the platform-specific style --> <draw:SkiaButton Text=\"Platform Button\" ControlStyle=\"Platform\" /> <!-- Explicitly use iOS style on any platform --> <draw:SkiaSwitch ControlStyle=\"Cupertino\" IsToggled=\"true\" /> Supported Controls The following controls support platform-specific styling: SkiaButton: Different button appearances across platforms SkiaSwitch: Toggle switches with platform-specific track and thumb styling SkiaCheckbox: Checkbox controls with platform-appropriate checkmarks and animations Platform Style Characteristics Cupertino (iOS) Style Rounded corners and subtle shadows Blue accent color (#007AFF) Switches have pill-shaped tracks with shadows on the thumb Buttons typically have semibold text Material (Android) Style Less rounded corners More pronounced shadows Material blue accent color (#2196F3) Switches have track colors that match the thumb when active Buttons often use uppercase text Windows Style Minimal corner radius Subtle shadows Windows blue accent color (#0078D7) Switches and buttons have a more squared appearance Customizing Platform Styles You can combine platform styles with custom styling. The platform style defines the base appearance, while your custom properties provide additional customization: <draw:SkiaButton Text=\"Custom Platform Button\" ControlStyle=\"Platform\" BackgroundColor=\"Purple\" TextColor=\"White\" /> This creates a button with the platform-specific shape, shadow, and behavior, but with your custom colors. Creating Custom Platform-Styled Controls If you're creating custom controls, you can leverage the same platform styling system: public class MyCustomControl : SkiaControl { public static readonly BindableProperty ControlStyleProperty = BindableProperty.Create( nameof(ControlStyle), typeof(PrebuiltControlStyle), typeof(MyCustomControl), PrebuiltControlStyle.Unset); public PrebuiltControlStyle ControlStyle { get { return (PrebuiltControlStyle)GetValue(ControlStyleProperty); } set { SetValue(ControlStyleProperty, value); } } protected override void OnPropertyChanged(string propertyName = null) { base.OnPropertyChanged(propertyName); if (propertyName == nameof(ControlStyle)) { ApplyPlatformStyle(); } } private void ApplyPlatformStyle() { switch (ControlStyle) { case PrebuiltControlStyle.Cupertino: // Apply iOS-specific styling break; case PrebuiltControlStyle.Material: // Apply Material Design styling break; case PrebuiltControlStyle.Windows: // Apply Windows styling break; case PrebuiltControlStyle.Platform: #if IOS || MACCATALYST // Apply iOS styling #elif ANDROID // Apply Material styling #elif WINDOWS // Apply Windows styling #endif break; } } }"}, "articles/advanced/skiascroll.html": {"href": "articles/advanced/skiascroll.html", "title": "Advanced Scrolling with SkiaScroll in DrawnUi.Maui | DrawnUi Documentation", "summary": "Advanced Scrolling with SkiaScroll in DrawnUi.Maui DrawnUi.Maui’s SkiaScroll control provides high-performance, flexible scrolling for custom UIs, games, dashboards, and data-heavy apps. This article covers advanced usage, virtualization, customization, and best practices for SkiaScroll and related controls. Why SkiaScroll? Smooth, pixel-perfect scrolling on all platforms Supports both vertical, horizontal, and bidirectional scrolling Virtualization for large data sets Customizable headers, footers, and overlays Pinch-to-zoom and gesture support Works with any DrawnUi content: layouts, images, shapes, etc. Basic Usage <DrawUi:SkiaScroll Orientation=\"Vertical\" WidthRequest=\"400\" HeightRequest=\"600\"> <DrawUi:SkiaLayout LayoutType=\"Column\" Spacing=\"10\"> <DrawUi:SkiaLabel Text=\"Item 1\" /> <DrawUi:SkiaLabel Text=\"Item 2\" /> <!-- More items --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Multi-Directional and Zoomable Scrolling <DrawUi:SkiaScroll Orientation=\"Both\" ZoomLocked=\"False\" ZoomMin=\"1\" ZoomMax=\"3\"> <DrawUi:SkiaLayout> <DrawUi:SkiaImage Source=\"large_map.jpg\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Virtualization for Large Data Sets Enable virtualization for smooth performance with thousands of items: <DrawUi:SkiaScroll UseVirtual=\"True\" Orientation=\"Vertical\"> <DrawUi:SkiaLayout LayoutType=\"Column\" ItemsSource=\"{Binding LargeItemCollection}\" VirtualizationMode=\"Enabled\"> <DrawUi:SkiaLayout.ItemTemplate> <DataTemplate> <DrawUi:SkiaLabel Text=\"{Binding Title}\" /> </DataTemplate> </DrawUi:SkiaLayout.ItemTemplate> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> UseVirtual on SkiaScroll enables virtualization. VirtualizationMode on SkiaLayout controls the strategy (Enabled, Smart, Managed). Combine with RecyclingTemplate for template reuse. Custom Headers, Footers, and Overlays <DrawUi:SkiaScroll HeaderSticky=\"True\" HeaderBehind=\"False\"> <DrawUi:SkiaScroll.Header> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"#3498DB\" HeightRequest=\"80\"> <DrawUi:SkiaLabel Text=\"Sticky Header\" TextColor=\"White\" FontSize=\"18\" /> </DrawUi:SkiaShape> </DrawUi:SkiaScroll.Header> <DrawUi:SkiaLayout LayoutType=\"Column\"> <!-- Content --> </DrawUi:SkiaLayout> <DrawUi:SkiaScroll.Footer> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"#2C3E50\" HeightRequest=\"60\"> <DrawUi:SkiaLabel Text=\"Footer\" TextColor=\"White\" /> </DrawUi:SkiaShape> </DrawUi:SkiaScroll.Footer> </DrawUi:SkiaScroll> Pull-to-Refresh <DrawUi:SkiaScroll x:Name=\"MyScrollView\" Refreshing=\"OnRefreshing\"> <DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:RefreshIndicator /> </DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:SkiaLayout LayoutType=\"Column\"> <!-- Content items --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> In code-behind: private async void OnRefreshing(object sender, EventArgs e) { // Perform refresh operation await LoadDataAsync(); ((SkiaScroll)sender).EndRefresh(); } Infinite and Looped Scrolling Use SkiaScrollLooped for banners, carousels, or infinite galleries: <DrawUi:SkiaScrollLooped Orientation=\"Horizontal\" IsBanner=\"True\" CycleSpace=\"100\"> <DrawUi:SkiaLayout LayoutType=\"Row\"> <DrawUi:SkiaImage Source=\"image1.jpg\" /> <DrawUi:SkiaImage Source=\"image2.jpg\" /> <!-- More images --> </DrawUi:SkiaLayout> </DrawUi:SkiaScrollLooped> Programmatic Scrolling and Position Tracking // Scroll to a specific position myScroll.ScrollToPosition(0, 500, true); // Animated scroll to Y=500 // Scroll to a child element myScroll.ScrollToView(targetElement, true); // Track scroll position float y = myScroll.ViewportOffsetY; Performance Tips Enable virtualization for large lists Use Cache=\"Operations\" for static or rarely-changing content Avoid nesting too many scrolls; prefer flat layouts Use SkiaLabelFps to monitor performance For custom drawing, override OnDraw in your content controls Advanced: Custom Scroll Effects and Gestures Implement parallax, sticky headers, or custom scroll physics by extending SkiaScroll Use gesture listeners for advanced input (drag, swipe, pinch) Combine with SkiaDrawer for overlay panels Summary SkiaScroll and related controls provide a robust, high-performance foundation for any scrolling UI in DrawnUi.Maui. With support for virtualization, zoom, custom overlays, and advanced gestures, you can build everything from chat apps to dashboards and games with smooth, responsive scrolling."}, "articles/controls/animations.html": {"href": "articles/controls/animations.html", "title": "Animation Controls | DrawnUi Documentation", "summary": "Animation Controls DrawnUi.Maui provides powerful controls for displaying animations directly on the canvas with high performance. This article covers the animation controls available in the framework. Animation Basics All animation controls in DrawnUi.Maui share common functionality through the AnimatedFramesRenderer base class. This provides consistent playback control and event handling across different animation types. Common properties and methods include: Property Type Description AutoPlay bool Automatically start animation when loaded IsPlaying bool Indicates if animation is currently playing Repeat int Number of times to repeat (-1 for infinite looping) SpeedRatio double Animation playback speed multiplier DefaultFrame int Frame to display when not playing Common methods: Start() - Begin or resume the animation Stop() - Pause the animation Seek(frame) - Jump to a specific frame Common events: Started - Fires when animation begins Finished - Fires when animation completes SkiaGif SkiaGif is a control for displaying animated GIF images with precise frame timing and control. Basic Usage <DrawUi:SkiaGif Source=\"animated.gif\" WidthRequest=\"200\" HeightRequest=\"200\" AutoPlay=\"True\" Repeat=\"-1\" /> Key Properties Property Type Description Source string Path or URL to the GIF file Animation GifAnimation Internal animation data (automatically created) Loading Sources SkiaGif can load animations from various sources: // From app resources myGif.Source = \"embedded_resource.gif\"; // From file system myGif.Source = \"file:///path/to/animation.gif\"; // From URL myGif.Source = \"https://example.com/animation.gif\"; Controlling Playback // Start the animation myGif.Start(); // Stop at current frame myGif.Stop(); // Jump to specific frame myGif.Seek(5); // Get total frames int total = myGif.Animation?.TotalFrames ?? 0; SkiaLottie SkiaLottie is a control for displaying Lottie animations, which are vector-based animations exported from Adobe After Effects. It provides smooth, resolution-independent animations with additional customization options. Basic Usage <DrawUi:SkiaLottie Source=\"animation.json\" WidthRequest=\"200\" HeightRequest=\"200\" AutoPlay=\"True\" Repeat=\"-1\" /> Toggle State Support SkiaLottie includes special support for toggle/switch animations with the IsOn property: <DrawUi:SkiaLottie Source=\"toggle_animation.json\" IsOn=\"{Binding IsToggled}\" DefaultFrame=\"0\" DefaultFrameWhenOn=\"30\" SpeedRatio=\"1.5\" AutoPlay=\"True\" Repeat=\"0\" /> This is perfect for animated toggles, checkboxes, or any other animation with distinct on/off states. Key Properties Property Type Description Source string Path or URL to the Lottie JSON file ColorTint Color Color tint applied to the entire animation Colors IList Collection of replacement colors IsOn bool Toggle state property DefaultFrameWhenOn int Frame to display when IsOn = true ApplyIsOnWhenNotPlaying bool Whether to apply IsOn state when not playing Customizing Colors One of the powerful features of SkiaLottie is color customization: <!-- Apply a global tint --> <DrawUi:SkiaLottie Source=\"animation.json\" ColorTint=\"Red\" /> For more granular control, you can replace multiple colors: // Replace specific colors in the animation myLottie.Colors.Add(Colors.Blue); myLottie.Colors.Add(Colors.Green); // Apply changes myLottie.ReloadSource(); SkiaSprite SkiaSprite is a high-performance control for displaying and animating sprite sheets. It loads sprite sheets (a single image containing multiple animation frames arranged in a grid) and renders individual frames with precise timing for smooth animations. Basic Usage <DrawUi:SkiaSprite Source=\"sprites/explosion.png\" Columns=\"8\" Rows=\"4\" FramesPerSecond=\"24\" AutoPlay=\"True\" Repeat=\"-1\" WidthRequest=\"128\" HeightRequest=\"128\" /> Key Properties Property Type Description Source string Path or URL of the sprite sheet image Columns int Number of columns in the sprite sheet grid Rows int Number of rows in the sprite sheet grid FramesPerSecond int Animation speed in frames per second (default: 24) MaxFrames int Maximum number of frames to use (0 means use all) CurrentFrame int Current frame being displayed (0-based index) FrameSequence int[] Custom sequence of frames to play AnimationName string Name of a predefined animation sequence Sprite Sheet Structure A sprite sheet is a single image containing multiple frames arranged in a grid: +---+---+---+---+ | 0 | 1 | 2 | 3 | +---+---+---+---+ | 4 | 5 | 6 | 7 | +---+---+---+---+ | 8 | 9 | 10| 11| +---+---+---+---+ The Columns and Rows properties define the grid structure: In the example above, set Columns=\"4\" and Rows=\"3\" Frames are numbered left-to-right, top-to-bottom (0 to 11) Each frame must have the same dimensions Frame Sequences and Reusing Spritesheets One of the key features of SkiaSprite is the ability to create multiple animations from a single spritesheet by defining frame sequences: // Register named animations for a character spritesheet SkiaSprite.CreateAnimationSequence(\"Idle\", new[] { 0, 1, 2, 1 }); SkiaSprite.CreateAnimationSequence(\"Walk\", new[] { 3, 4, 5, 6, 7, 8 }); SkiaSprite.CreateAnimationSequence(\"Jump\", new[] { 9, 10, 11 }); Then in XAML just reference by name: <!-- Multiple sprites sharing the same spritesheet with different animations --> <DrawUi:SkiaSprite Source=\"character.png\" AnimationName=\"Walk\" /> <DrawUi:SkiaSprite Source=\"character.png\" AnimationName=\"Jump\" /> Or use a direct frame sequence: // Define a specific frame sequence mySprite.FrameSequence = new[] { 3, 4, 5, 4, 3 }; // Play frames in this exact order Spritesheet Caching SkiaSprite includes an intelligent caching system to avoid reloading the same spritesheets multiple times: // Clear the entire spritesheet cache SkiaSprite.ClearCache(); // Remove a specific spritesheet from cache SkiaSprite.RemoveFromCache(\"character.png\"); Performance Considerations SkiaGif GIFs can consume significant memory, especially large ones For large animations, verify memory usage SkiaLottie Vector-based animations are more memory efficient than GIFs Complex Lottie animations may be CPU-intensive Use ColorTint for simple color changes rather than individual color replacements when possible SkiaSprite Spritesheets are cached automatically to avoid redundant loading For large or numerous sprite sheets, consider monitoring memory usage Use ClearCache() or RemoveFromCache() when spritesheets are no longer needed For complex animations, use frame sequences to avoid redundant frames Examples Loading Animation with Lottie <DrawUi:SkiaLottie Source=\"loading_spinner.json\" WidthRequest=\"48\" HeightRequest=\"48\" AutoPlay=\"True\" Repeat=\"-1\" /> Animated Button with Sprite Sheet <DrawUi:SkiaButton WidthRequest=\"200\" HeightRequest=\"60\" BackgroundColor=\"Transparent\"> <DrawUi:SkiaSprite x:Name=\"buttonAnimation\" Source=\"button_animation.png\" Columns=\"5\" Rows=\"1\" FramesPerSecond=\"30\" AutoPlay=\"False\" DefaultFrame=\"0\" /> <DrawUi:SkiaLabel Text=\"Animated Button\" TextColor=\"White\" FontSize=\"16\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaButton> In code-behind: MyButton.Pressed += (s, e) => { buttonAnimation.Stop(); buttonAnimation.CurrentFrame = 0; buttonAnimation.Start(); }; Game Character Animation <DrawUi:SkiaLayout WidthRequest=\"200\" HeightRequest=\"200\"> <DrawUi:SkiaSprite x:Name=\"CharacterAnimation\" Source=\"character_sprites.png\" Columns=\"8\" Rows=\"4\" FramesPerSecond=\"12\" AnimationName=\"Walk\" AutoPlay=\"True\" WidthRequest=\"128\" HeightRequest=\"128\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> In code-behind: // Setup animation sequences void InitializeAnimations() { SkiaSprite.CreateAnimationSequence(\"Idle\", new[] { 0, 1, 2, 1 }); SkiaSprite.CreateAnimationSequence(\"Walk\", new[] { 8, 9, 10, 11, 12, 13, 14, 15 }); SkiaSprite.CreateAnimationSequence(\"Jump\", new[] { 16, 17, 18, 19, 20, 21 }); SkiaSprite.CreateAnimationSequence(\"Attack\", new[] { 24, 25, 26, 27, 28, 29, 30 }); } // Change animation based on game state void UpdateCharacterState(PlayerState state) { CharacterAnimation.Stop(); switch (state) { case PlayerState.Idle: CharacterAnimation.AnimationName = \"Idle\"; break; case PlayerState.Walking: CharacterAnimation.AnimationName = \"Walk\"; break; case PlayerState.Jumping: CharacterAnimation.AnimationName = \"Jump\"; break; case PlayerState.Attacking: CharacterAnimation.AnimationName = \"Attack\"; break; } CharacterAnimation.Start(); } GIF Avatar <draw:SkiaShape Type=\"Circle\" WidthRequest=\"100\" LockRatio=\"1\"> <draw:SkiaGif Source=\"avatar.gif\" WidthRequest=\"100\" LockRatio=\"1\" AutoPlay=\"True\" Repeat=\"-1\" /> </draw:SkiaShape>"}, "articles/controls/buttons.html": {"href": "articles/controls/buttons.html", "title": "Button Controls | DrawnUi Documentation", "summary": "Button Controls DrawnUi provides highly customizable button controls with platform-specific styling and support for custom content. SkiaButton SkiaButton is a versatile button control supporting different button styles, platform-specific appearance, and custom content. You can use the default content or provide your own child views. If you use conventional tags (BtnText, BtnShape), SkiaButton will apply its properties (like Text, TextColor, etc.) to those views automatically. Note: If you provide custom content, use the tags BtnText for your main label and BtnShape for the button background to enable property binding. Basic Usage <draw:SkiaButton Text=\"Click Me\" WidthRequest=\"120\" HeightRequest=\"40\" BackgroundColor=\"Blue\" TextColor=\"White\" CornerRadius=\"8\" Clicked=\"OnButtonClicked\" /> Custom Content Example <draw:SkiaButton> <draw:SkiaShape Tag=\"BtnShape\" BackgroundColor=\"Red\" CornerRadius=\"12\" /> <draw:SkiaLabel Tag=\"BtnText\" Text=\"Custom\" TextColor=\"Yellow\" /> </draw:SkiaButton> Button Style Types SkiaButton supports multiple style variants through the ButtonStyle property: Contained: Standard filled button with background color (default) Outlined: Button with outline border and transparent background Text: Button with no background or border, only text <draw:SkiaButton Text=\"Outlined Button\" ButtonStyle=\"Outlined\" BackgroundColor=\"Blue\" TextColor=\"Blue\" /> Platform-Specific Styling Platform-specific styles are selected automatically or can be set in code via the UsingControlStyle property (not bindable in XAML). Styles include: Cupertino: iOS-style button Material: Android Material Design button Windows: Windows-style button Note: There is no ControlStyle bindable property. Platform style is set internally or in code. Elevation Buttons can have elevation (shadow) effects: <draw:SkiaButton Text=\"Elevated Button\" ElevationEnabled=\"True\" /> Properties Property Type Description Text string The text displayed on the button TextColor Color The color of the button text BackgroundColor Color The background color of the button CornerRadius float The corner radius of the button (applied via BtnShape) ButtonStyle ButtonStyleType The button style (Contained, Outlined, Text) ElevationEnabled bool Whether the button has a shadow effect TextCase TextTransform The text case transformation (None, Uppercase, Lowercase) FontSize double The font size of the button text FontFamily string The font family of the button text IsDisabled bool Disables the button if true IsPressed bool True while the button is pressed IconPosition IconPositionType Position of icon (icon support planned) ApplyEffect SkiaTouchAnimation Touch animation effect (Ripple, Shimmer, etc.) Events Clicked: Raised when the button is clicked/tapped Pressed: Raised when the button is pressed down Released: Raised when the button is released Up, Down, Tapped: Additional gesture events Icon Support Icon support is planned. The IconPosition property exists, but icon rendering is not yet implemented. API XML Documentation The following methods in SkiaButton have been updated with XML documentation in the codebase: OnDown, OnUp, OnTapped, ApplyProperties, CreateDefaultContent, CreateCupertinoStyleContent, CreateMaterialStyleContent, CreateWindowsStyleContent, OnButtonPropertyChanged, FindViews, CreateClip. For more details, see the source code in src/Engine/Maui/Controls/Button/SkiaButton.cs."}, "articles/controls/carousels.html": {"href": "articles/controls/carousels.html", "title": "Carousel Controls | DrawnUi Documentation", "summary": "Carousel Controls DrawnUi.<PERSON><PERSON> provides powerful carousel controls for creating interactive, swipeable displays of content. This article covers the carousel components available in the framework. SkiaCarousel SkiaCarousel is a specialized scroll control designed specifically for creating swipeable carousels with automatic snapping to items. Basic Usage <DrawUi:SkiaCarousel WidthRequest=\"400\" HeightRequest=\"200\" SelectedIndex=\"0\"> <!-- Item 1 --> <DrawUi:SkiaLayout BackgroundColor=\"Red\"> <DrawUi:SkiaLabel Text=\"Slide 1\" FontSize=\"24\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> <!-- Item 2 --> <DrawUi:SkiaLayout BackgroundColor=\"Green\"> <DrawUi:SkiaLabel Text=\"Slide 2\" FontSize=\"24\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> <!-- Item 3 --> <DrawUi:SkiaLayout BackgroundColor=\"Blue\"> <DrawUi:SkiaLabel Text=\"Slide 3\" FontSize=\"24\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaCarousel> Key Properties Property Type Description SelectedIndex int Current selected item index InTransition bool Indicates if carousel is currently transitioning Spacing float Space between carousel items SidesOffset float Side padding to create a peek effect Bounces bool Enables bouncing effect at edges ItemsSource IEnumerable Data source for dynamically generating items ItemTemplate DataTemplate Template for items when using ItemsSource Peek Next/Previous Items You can create a peek effect to show portions of adjacent slides: <DrawUi:SkiaCarousel WidthRequest=\"400\" HeightRequest=\"200\" SidesOffset=\"40\" SelectedIndex=\"0\"> <!-- Items here --> </DrawUi:SkiaCarousel> With SidesOffset=\"40\", 40 pixels on each side will be reserved to show portions of the previous and next items. Data Binding SkiaCarousel supports data binding through ItemsSource and ItemTemplate: <DrawUi:SkiaCarousel WidthRequest=\"400\" HeightRequest=\"200\" ItemsSource=\"{Binding CarouselItems}\"> <DrawUi:SkiaCarousel.ItemTemplate> <DataTemplate> <DrawUi:SkiaLayout BackgroundColor=\"{Binding Color}\"> <DrawUi:SkiaLabel Text=\"{Binding Title}\" FontSize=\"24\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> </DataTemplate> </DrawUi:SkiaCarousel.ItemTemplate> </DrawUi:SkiaCarousel> Tracking Current Item You can bind to the current item or monitor transitions: <DrawUi:SkiaCarousel x:Name=\"MyCarousel\" SelectedIndex=\"{Binding CurrentIndex, Mode=TwoWay}\" WidthRequest=\"400\" HeightRequest=\"200\"> <!-- Items here --> </DrawUi:SkiaCarousel> <!-- Display current state --> <DrawUi:SkiaLabel Text=\"{Binding Source={x:Reference MyCarousel}, Path=SelectedIndex, StringFormat='Current: {0}'}\" TextColor=\"Black\" /> <DrawUi:SkiaLabel Text=\"{Binding Source={x:Reference MyCarousel}, Path=InTransition, StringFormat='In Transition: {0}'}\" TextColor=\"Black\" /> The InTransition property is particularly useful for disabling user interactions during transitions. Programmatic Control You can control the carousel programmatically: // Jump to a specific index myCarousel.SelectedIndex = 2; // Animate to a specific index myCarousel.ScrollTo(2, true); // Track selection changes myCarousel.PropertyChanged += (sender, e) => { if (e.PropertyName == nameof(SkiaCarousel.SelectedIndex)) { // Handle selection change var index = myCarousel.SelectedIndex; } }; Advanced Examples Image Gallery Carousel <DrawUi:SkiaCarousel WidthRequest=\"400\" HeightRequest=\"300\" SidesOffset=\"50\" Bounces=\"True\"> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"12\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaImage Source=\"image1.jpg\" Aspect=\"AspectFill\" /> </DrawUi:SkiaShape> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"12\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaImage Source=\"image2.jpg\" Aspect=\"AspectFill\" /> </DrawUi:SkiaShape> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"12\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaImage Source=\"image3.jpg\" Aspect=\"AspectFill\" /> </DrawUi:SkiaShape> </DrawUi:SkiaCarousel> Card Carousel with Indicators <DrawUi:SkiaLayout LayoutType=\"Column\" HorizontalOptions=\"Fill\"> <DrawUi:SkiaCarousel x:Name=\"CardCarousel\" WidthRequest=\"400\" HeightRequest=\"300\" SelectedIndex=\"{Binding CurrentCardIndex, Mode=TwoWay}\"> <!-- Card items here --> </DrawUi:SkiaCarousel> <!-- Page indicators --> <DrawUi:SkiaLayout LayoutType=\"Row\" Spacing=\"8\" HorizontalOptions=\"Center\" Margin=\"0,16,0,0\"> <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"12\" HeightRequest=\"12\" BackgroundColor=\"{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=0}\" /> <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"12\" HeightRequest=\"12\" BackgroundColor=\"{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=1}\" /> <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"12\" HeightRequest=\"12\" BackgroundColor=\"{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=2}\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaLayout> With a converter to change color based on selection: public class SelectedIndexConverter : IValueConverter { public object Convert(object value, Type targetType, object parameter, CultureInfo culture) { int selectedIndex = (int)value; int targetIndex = int.Parse(parameter.ToString()); return selectedIndex == targetIndex ? Colors.Blue : Colors.LightGray; } public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) { throw new NotImplementedException(); } } Performance Considerations For optimal performance, use Cache=\"Operations\" or Cache=\"Image\" on complex carousel items Avoid placing too many items directly in the carousel; use virtualization through ItemsSource for large collections Consider using lightweight content for peek items if they'll be partially visible most of the time Monitor the performance using SkiaLabelFps during development to ensure smooth scrolling"}, "articles/controls/drawers.html": {"href": "articles/controls/drawers.html", "title": "Drawer Controls | DrawnUi Documentation", "summary": "Drawer Controls DrawnUi.<PERSON><PERSON> provides powerful drawer controls for creating sliding panels that can appear from any edge of the screen. This article covers the drawer components available in the framework. SkiaDrawer SkiaDrawer is a versatile control that provides a sliding panel (drawer) with animated transitions and gesture support. It can slide in from any edge, making it perfect for navigation menus, filter panels, property drawers, and more. Basic Usage <DrawUi:SkiaDrawer Direction=\"FromBottom\" HeaderSize=\"60\" IsOpen=\"False\" HeightRequest=\"500\" HorizontalOptions=\"Fill\" VerticalOptions=\"End\"> <DrawUi:SkiaLayout HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <!-- Header (visible when drawer is closed) --> <DrawUi:SkiaShape BackgroundColor=\"Blue\" CornerRadius=\"20,20,0,0\" HeightRequest=\"60\" HorizontalOptions=\"Fill\"> <DrawUi:SkiaLabel Text=\"Drag Me\" TextColor=\"White\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> <!-- Content (scrolls within drawer) --> <DrawUi:SkiaLayout BackgroundColor=\"White\" Padding=\"20\" Type=\"Column\" Spacing=\"16\" AddMarginTop=\"60\"> <DrawUi:SkiaLabel Text=\"Drawer Content\" FontSize=\"20\" TextColor=\"Black\" /> <!-- Additional content --> </DrawUi:SkiaLayout> </DrawUi:SkiaLayout> </DrawUi:SkiaDrawer> Key Properties Property Type Description Direction DrawerDirection Direction from which the drawer appears HeaderSize double Size of the area that remains visible when drawer is closed IsOpen bool Controls whether the drawer is open or closed AmplitudeSize double Optional override for drawer movement calculation Drawer Direction The Direction property controls the edge from which the drawer appears: <!-- Bottom drawer --> <DrawUi:SkiaDrawer Direction=\"FromBottom\" VerticalOptions=\"End\"> <!-- Content --> </DrawUi:SkiaDrawer> <!-- Top drawer --> <DrawUi:SkiaDrawer Direction=\"FromTop\" VerticalOptions=\"Start\"> <!-- Content --> </DrawUi:SkiaDrawer> <!-- Left drawer --> <DrawUi:SkiaDrawer Direction=\"FromLeft\" HorizontalOptions=\"Start\"> <!-- Content --> </DrawUi:SkiaDrawer> <!-- Right drawer --> <DrawUi:SkiaDrawer Direction=\"FromRight\" HorizontalOptions=\"End\"> <!-- Content --> </DrawUi:SkiaDrawer> Note that you should set the appropriate alignment options (VerticalOptions and HorizontalOptions) to match the drawer direction. Header and Content The drawer typically consists of two main parts: Header: Remains partially or fully visible when the drawer is closed Content: The main body of the drawer that slides in and out The HeaderSize property determines how much of the drawer remains visible when closed. Controlling the Drawer You can control the drawer programmatically: // Open the drawer myDrawer.IsOpen = true; // Close the drawer myDrawer.IsOpen = false; // Toggle the drawer myDrawer.IsOpen = !myDrawer.IsOpen; You can also use binding: <DrawUi:SkiaDrawer IsOpen=\"{Binding IsDrawerOpen, Mode=TwoWay}\"> <!-- Content --> </DrawUi:SkiaDrawer> Commands SkiaDrawer provides built-in commands for programmatic control: <DrawUi:SkiaButton Text=\"Open Drawer\" CommandTapped=\"{Binding Source={x:Reference MyDrawer}, Path=CommandOpen}\" /> <DrawUi:SkiaButton Text=\"Close Drawer\" CommandTapped=\"{Binding Source={x:Reference MyDrawer}, Path=CommandClose}\" /> <DrawUi:SkiaButton Text=\"Toggle Drawer\" CommandTapped=\"{Binding Source={x:Reference MyDrawer}, Path=CommandToggle}\" /> Scrollable Drawer Content For scrollable content within the drawer, combine with SkiaScroll: <DrawUi:SkiaDrawer x:Name=\"BottomDrawer\" Direction=\"FromBottom\" HeaderSize=\"60\" HeightRequest=\"500\" VerticalOptions=\"End\"> <DrawUi:SkiaLayout> <!-- Header --> <DrawUi:SkiaShape BackgroundColor=\"Blue\" HeightRequest=\"60\" CornerRadius=\"20,20,0,0\"> <!-- Header content --> </DrawUi:SkiaShape> <!-- Scrollable content --> <DrawUi:SkiaScroll AddMarginTop=\"60\" Bounces=\"False\" BackgroundColor=\"White\"> <DrawUi:SkiaLayout Type=\"Column\" Padding=\"20\" Spacing=\"16\"> <!-- Many items here --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> </DrawUi:SkiaLayout> </DrawUi:SkiaDrawer> The AddMarginTop property on SkiaScroll helps create space for the header. Examples Bottom Sheet with Form <DrawUi:SkiaDrawer Direction=\"FromBottom\" HeaderSize=\"60\" IsOpen=\"False\" HeightRequest=\"400\" VerticalOptions=\"End\" HorizontalOptions=\"Fill\"> <DrawUi:SkiaLayout> <!-- Header --> <DrawUi:SkiaShape BackgroundColor=\"#3498DB\" CornerRadius=\"20,20,0,0\" HeightRequest=\"60\"> <DrawUi:SkiaLayout HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <DrawUi:SkiaLabel Text=\"Settings\" TextColor=\"White\" FontSize=\"18\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> <DrawUi:SkiaShape Type=\"Rectangle\" WidthRequest=\"40\" HeightRequest=\"4\" CornerRadius=\"2\" BackgroundColor=\"White\" Margin=\"0,10,0,0\" HorizontalOptions=\"Center\" VerticalOptions=\"Start\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaShape> <!-- Content --> <DrawUi:SkiaScroll AddMarginTop=\"60\" BackgroundColor=\"White\"> <DrawUi:SkiaLayout Type=\"Column\" Padding=\"20\" Spacing=\"16\"> <!-- Form fields --> <DrawUi:SkiaLabel Text=\"Username\" FontSize=\"14\" TextColor=\"#333333\" /> <DrawUi:SkiaMauiEntry PlaceholderText=\"Enter your username\" HeightRequest=\"50\" BackgroundColor=\"#F5F5F5\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"Email\" FontSize=\"14\" TextColor=\"#333333\" /> <DrawUi:SkiaMauiEntry PlaceholderText=\"Enter your email\" HeightRequest=\"50\" BackgroundColor=\"#F5F5F5\" TextColor=\"#333333\" /> <DrawUi:SkiaButton Text=\"SAVE\" BackgroundColor=\"#2ECC71\" TextColor=\"White\" HeightRequest=\"50\" Margin=\"0,20,0,0\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> </DrawUi:SkiaLayout> </DrawUi:SkiaDrawer> Navigation Drawer <Grid> <!-- Main content --> <DrawUi:Canvas HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <DrawUi:SkiaLayout BackgroundColor=\"White\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <!-- Main app content here --> <DrawUi:SkiaButton Text=\"Open Menu\" CommandTapped=\"{Binding Source={x:Reference SideDrawer}, Path=CommandOpen}\" HorizontalOptions=\"Start\" VerticalOptions=\"Start\" Margin=\"20\" /> </DrawUi:SkiaLayout> </DrawUi:Canvas> <!-- Left side drawer --> <DrawUi:SkiaDrawer x:Name=\"SideDrawer\" Direction=\"FromLeft\" HeaderSize=\"0\" IsOpen=\"False\" WidthRequest=\"280\" HorizontalOptions=\"Start\" VerticalOptions=\"Fill\"> <DrawUi:SkiaLayout BackgroundColor=\"#2C3E50\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <DrawUi:SkiaScroll> <DrawUi:SkiaLayout Type=\"Column\" Padding=\"0,40,0,0\"> <!-- User info --> <DrawUi:SkiaLayout Type=\"Column\" Padding=\"20\" Spacing=\"8\"> <DrawUi:SkiaShape Type=\"Circle\" WidthRequest=\"80\" HeightRequest=\"80\" BackgroundColor=\"#3498DB\"> <DrawUi:SkiaLabel Text=\"JD\" FontSize=\"36\" TextColor=\"White\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> <DrawUi:SkiaLabel Text=\"John Doe\" FontSize=\"18\" TextColor=\"White\" Margin=\"0,10,0,0\" /> <DrawUi:SkiaLabel Text=\"<EMAIL>\" FontSize=\"14\" TextColor=\"#BBBBBB\" /> </DrawUi:SkiaLayout> <!-- Menu items --> <DrawUi:SkiaShape Type=\"Rectangle\" HeightRequest=\"1\" BackgroundColor=\"#405060\" Margin=\"0,20,0,20\" /> <!-- Menu item 1 --> <DrawUi:SkiaHotspot Tapped=\"OnMenuItemTapped\"> <DrawUi:SkiaLayout Type=\"Row\" Padding=\"20,15\" Spacing=\"16\"> <DrawUi:SkiaShape Type=\"Rectangle\" WidthRequest=\"24\" HeightRequest=\"24\" BackgroundColor=\"#3498DB\" /> <DrawUi:SkiaLabel Text=\"Home\" FontSize=\"16\" TextColor=\"White\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaHotspot> <!-- Additional menu items --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> </DrawUi:SkiaLayout> </DrawUi:SkiaDrawer> </Grid> Performance Considerations For complex drawers, consider using Cache=\"Operations\" on content that doesn't change often Use appropriate header size to ensure smooth gestures in the grabbable area For large drawers with many child elements, enable virtualization in nested scrolling content Avoid doing heavy work in IsOpen change handlers as this can cause animation stuttering"}, "articles/controls/images.html": {"href": "articles/controls/images.html", "title": "Image Controls | DrawnUi Documentation", "summary": "Image Controls DrawnUi.Maui provides powerful image controls for high-performance image rendering with advanced features like effects, transformations, and sophisticated caching. This article covers the image components available in the framework. SkiaImage SkiaImage is the core image control in DrawnUi.Maui, providing efficient image loading, rendering, and manipulation capabilities with direct SkiaSharp rendering. Basic Usage <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"AspectFit\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" WidthRequest=\"200\" HeightRequest=\"200\" /> Key Properties Property Type Description Source ImageSource Source of the image (URL, file, resource, stream) Aspect TransformAspect How the image scales to fit (AspectFit, AspectFill, etc.) HorizontalAlignment DrawImageAlignment Horizontal positioning of the image VerticalAlignment DrawImageAlignment Vertical positioning of the image RescalingQuality SKFilterQuality Quality of image rescaling LoadSourceOnFirstDraw bool Whether to defer loading until first render PreviewBase64 string Base64 encoded preview image to show while loading ImageBitmap LoadedImageSource Loaded image source (internal representation) AddEffect SkiaImageEffect Built-in image effect (<PERSON>, <PERSON><PERSON>, Tint, etc.) ColorTint Color Tint color for image effect Brightness double Adjusts image brightness Contrast double Adjusts image contrast Saturation double Adjusts image saturation Blur double Applies blur effect Gamma double Adjusts gamma Darken double Darkens the image Lighten double Lightens the image ZoomX/ZoomY double Zoom/scaling factors HorizontalOffset/VerticalOffset double Offset for image position SpriteWidth/SpriteHeight double Sprite sheet cell size SpriteIndex int Index of sprite to display Note: The UseCache property is not directly on SkiaImage, but caching is handled by the SkiaControl base class or internally. You can set Cache on SkiaImage for caching strategies (e.g., Cache=\"Image\"). Note: The VisualEffects property is inherited from SkiaControl. You can use <DrawUi:SkiaControl.VisualEffects> in XAML to apply effects like drop shadow or color presets. Aspect Modes The Aspect property controls how the image is sized and positioned within its container. This is a critical property for ensuring that your images display correctly while maintaining their proportions when appropriate. Available Aspect Modes Aspect Mode Description Visual Effect AspectFit Maintains aspect ratio while ensuring the entire image fits within available space May leave empty space at sides or top/bottom AspectFill Maintains aspect ratio while filling the entire space May crop portions of the image that don't fit Fill Stretches the image to fill the entire space May distort the image proportions Center Centers the image at its original size May clip or leave empty space depending on size TopLeft Positions the image at original size in the top-left corner May clip or leave empty space TopCenter Positions the image at original size at the top-center May clip or leave empty space TopRight Positions the image at original size in the top-right corner May clip or leave empty space CenterLeft Positions the image at original size at the center-left May clip or leave empty space CenterRight Positions the image at original size at the center-right May clip or leave empty space BottomLeft Positions the image at original size in the bottom-left corner May clip or leave empty space BottomCenter Positions the image at original size at the bottom-center May clip or leave empty space BottomRight Positions the image at original size in the bottom-right corner May clip or leave empty space ScaleDown Like AspectFit, but only scales down, never up Small images remain at original size AspectRatioWidth Maintains aspect ratio as determined by width Sets the height based on image aspect ratio AspectRatioHeight Maintains aspect ratio as determined by height Sets the width based on image aspect ratio AspectCover Same as AspectFill Alternative name for AspectFill Examples and Visual Guide <!-- Maintain aspect ratio, fit within bounds --> <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"AspectFit\" /> This ensures the entire image is visible, possibly with letterboxing (empty space) on the sides or top/bottom. <!-- Maintain aspect ratio, fill bounds (may crop) --> <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"AspectFill\" /> This fills the entire control with the image, possibly cropping parts that don't fit. Great for background images or thumbnails. <!-- Stretch to fill bounds (may distort) --> <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"Fill\" /> This stretches the image to fill the control exactly, potentially distorting the image proportions. <!-- Center the image without scaling --> <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"Center\" /> This displays the image at its original size, centered in the control. Parts may be clipped if the image is larger than the control. Combining Aspect and Alignment You can combine Aspect with HorizontalAlignment and VerticalAlignment for precise control: <!-- AspectFit with custom alignment --> <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"AspectFit\" HorizontalAlignment=\"Start\" VerticalAlignment=\"End\" /> This would fit the image within bounds while aligning it to the bottom-left corner of the available space. Choosing the Right Aspect Mode For user photos or content images: AspectFit ensures the entire image is visible For backgrounds or covers: AspectFill ensures no empty space is visible For icons that need to fill a specific area: Fill may be appropriate For pixel-perfect icons: Center or other position-specific modes maintain original dimensions For responsive layouts: AspectRatioWidth or AspectRatioHeight can help maintain proportions while adapting to container changes Image Alignment Control the alignment of the image within its container: <DrawUi:SkiaImage Source=\"image.png\" Aspect=\"AspectFit\" HorizontalAlignment=\"End\" VerticalAlignment=\"Start\" /> This will position the image at the top-right of its container. Image Effects SkiaImage supports various built-in effects through the AddEffect property: <!-- Apply a sepia effect --> <DrawUi:SkiaImage Source=\"image.png\" AddEffect=\"Sepia\" /> <!-- Apply a tint effect --> <DrawUi:SkiaImage Source=\"image.png\" AddEffect=\"Tint\" ColorTint=\"Red\" /> <!-- Apply grayscale effect --> <DrawUi:SkiaImage Source=\"image.png\" AddEffect=\"BlackAndWhite\" /> Image Adjustments Fine-tune image appearance with various adjustment properties: <DrawUi:SkiaImage Source=\"image.png\" Brightness=\"1.2\" Contrast=\"1.1\" Saturation=\"0.8\" Blur=\"2\" /> Advanced Effects For more complex effects, use the VisualEffects collection: <DrawUi:SkiaImage Source=\"image.png\"> <DrawUi:SkiaControl.VisualEffects> <DrawUi:DropShadowEffect Blur=\"8\" X=\"2\" Y=\"2\" Color=\"#80000000\" /> <DrawUi:ChainColorPresetEffect Preset=\"Sepia\" /> </DrawUi:SkiaControl.VisualEffects> </DrawUi:SkiaImage> Sprite Sheets SkiaImage supports sprite sheets for displaying a single sprite from a larger image: <DrawUi:SkiaImage Source=\"sprite-sheet.png\" SpriteWidth=\"64\" SpriteHeight=\"64\" SpriteIndex=\"2\" /> This shows the third sprite (index 2) from the sprite sheet, assuming each sprite is 64x64 pixels. Preview Images Show a low-resolution placeholder while loading the main image: <DrawUi:SkiaImage Source=\"https://example.com/large-image.jpg\" PreviewBase64=\"data:image/png;base64,iVBORw0KGgoAA...\" Aspect=\"AspectFit\" /> Loading Options Control how and when images are loaded: <!-- Immediate loading (default) --> <DrawUi:SkiaImage Source=\"image.png\" LoadSourceOnFirstDraw=\"False\" /> <!-- Deferred loading (load when first rendered) --> <DrawUi:SkiaImage Source=\"image.png\" LoadSourceOnFirstDraw=\"True\" /> Caching Strategies Optimize performance with various caching options: <!-- Use double-buffered image caching (good for changing content) --> <DrawUi:SkiaImage Source=\"image.png\" UseCache=\"ImageDoubleBuffered\" /> <!-- Use simple image caching (good for static content) --> <DrawUi:SkiaImage Source=\"image.png\" UseCache=\"Image\" /> <!-- Cache drawing operations rather than bitmap (memory efficient) --> <DrawUi:SkiaImage Source=\"image.png\" UseCache=\"Operations\" /> <!-- No caching (for frequently changing images) --> <DrawUi:SkiaImage Source=\"image.png\" UseCache=\"None\" /> Handling Load Events You can respond to image load success or failure in code-behind: public MainPage() { InitializeComponent(); MyImage.OnSuccess += (sender, e) => { // Image loaded successfully }; MyImage.OnError += (sender, e) => { // Image failed to load // e.Contains the error information }; } Image Management SkiaImageManager DrawnUi.Maui includes a powerful image management system through the SkiaImageManager class. This provides centralized image loading, caching, and resource management. Preloading Images Preload images to ensure they're ready when needed: // Preload a single image await SkiaImageManager.Instance.PreloadImage(\"Images/my-image.jpg\"); // Preload multiple images await SkiaImageManager.Instance.PreloadImages(new List<string> { \"Images/image1.jpg\", \"Images/image2.jpg\", \"Images/image3.jpg\" }); Managing Memory Usage Configure the image manager for optimal memory usage: // Enable bitmap reuse for better memory usage SkiaImageManager.Instance.ReuseBitmaps = true; // Set the maximum cache size (in bytes) SkiaImageManager.Instance.MaxCacheSize = 50 * 1024 * 1024; // 50MB // Clear unused cached images SkiaImageManager.Instance.ClearUnusedImages(); // Clear all cached images SkiaImageManager.Instance.ClearAll(); Advanced Usage Loading from Base64 Load images directly from base64 strings: var base64String = \"data:image/png;base64,iVBORw0KGgoAA...\"; myImage.SetFromBase64(base64String); Applying Transformations Apply transformations to the displayed image: <DrawUi:SkiaImage Source=\"image.png\" ZoomX=\"1.2\" ZoomY=\"1.2\" HorizontalOffset=\"10\" VerticalOffset=\"-5\" /> Creating Images in Code Create and configure SkiaImage controls programmatically: var image = new SkiaImage { Source = \"Images/my-image.jpg\", LoadSourceOnFirstDraw = false, Aspect = Aspect.AspectFit, RescalingQuality = SKFilterQuality.Medium, AddEffect = SkiaImageEffect.Sepia, WidthRequest = 200, HeightRequest = 200, HorizontalOptions = LayoutOptions.Center, VerticalOptions = LayoutOptions.Center }; myLayout.Children.Add(image); Performance Considerations Optimization Tips Image Size Resize images to their display size before including in your app Use compressed formats (WebP, optimized PNG/JPEG) when possible Consider providing different image sizes for different screen densities Caching Use UseCache=\"Image\" for static images that don't change Use UseCache=\"ImageDoubleBuffered\" for images that change occasionally Use UseCache=\"Operations\" for images with effects but static content Use UseCache=\"None\" only for frequently changing images Loading Strategy Use LoadSourceOnFirstDraw=\"True\" for off-screen images Preload important images with SkiaImageManager.PreloadImages() Provide preview images with PreviewBase64 for large remote images Rendering Quality Set appropriate RescalingQuality based on your needs: None: Fastest but lowest quality Low: Good balance for scrolling content Medium: Good for static content High: Best quality but slowest (use sparingly) Memory Management Enable bitmap reuse with SkiaImageManager.Instance.ReuseBitmaps = true Set reasonable cache limits with MaxCacheSize Call ClearUnusedImages() when appropriate Examples of Optimized Image Loading For Lists/Carousels <DrawUi:SkiaImage Source=\"{Binding ImageUrl}\" LoadSourceOnFirstDraw=\"True\" UseCache=\"ImageDoubleBuffered\" RescalingQuality=\"Low\" Aspect=\"AspectFill\" /> For Hero/Cover Images <DrawUi:SkiaImage Source=\"{Binding CoverImage}\" PreviewBase64=\"{Binding CoverImagePreview}\" LoadSourceOnFirstDraw=\"False\" UseCache=\"Image\" RescalingQuality=\"Medium\" Aspect=\"AspectFill\" /> For Image Galleries <DrawUi:SkiaScroll Orientation=\"Horizontal\"> <DrawUi:SkiaLayout LayoutType=\"Row\" Spacing=\"10\"> <!-- Images that are initially visible --> <DrawUi:SkiaImage Source=\"{Binding Images[0]}\" LoadSourceOnFirstDraw=\"False\" UseCache=\"Image\" WidthRequest=\"300\" HeightRequest=\"200\" /> <!-- Images that may be scrolled to --> <DrawUi:SkiaImage Source=\"{Binding Images[1]}\" LoadSourceOnFirstDraw=\"True\" UseCache=\"Image\" WidthRequest=\"300\" HeightRequest=\"200\" /> <!-- More images... --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll>"}, "articles/controls/index.html": {"href": "articles/controls/index.html", "title": "Controls Overview | DrawnUi Documentation", "summary": "Controls Overview DrawnUi positions itsself as an angine providing a toolset to create and use custom drawn controls. Out-of-the box it provides you with base controls that can be used a lego-bricks to composite custom controls, and proposes some useful pre-made custom controls. The main spirit is to have all controlls subclassable and customizable at the maximum possible extent. DrawnUi provides a comprehensive set of UI controls rendered with SkiaSharp for optimal performance. All controls support platform-specific styling and extensive customization options. Control Categories DrawnUi controls are organized into several categories: Button Controls SkiaButton: Standard button with platform-specific styling Custom button variants: Outlined, text-only, and other button styles Toggle Controls SkiaSwitch: Platform-styled toggle switch SkiaCheckbox: Platform-styled checkbox SkiaToggle: Base toggle class for custom toggles Layout Controls SkiaLayout: Base layout container GridLayout: Grid-based layout HStack/VStack: Horizontal and vertical stack layouts Text Controls SkiaLabel: High-performance text rendering SkiaMarkdownLabel: Markdown-capable text control Image Controls SkiaImage: High-performance image rendering SkiaSvg: SVG rendering SkiaGif: Animated GIF support"}, "articles/controls/layouts.html": {"href": "articles/controls/layouts.html", "title": "Layout Controls | DrawnUi Documentation", "summary": "Layout Controls DrawnUi.Maui provides a powerful and flexible layout system for arranging controls with high performance. The system is similar to MAUI's native layout system but optimized for direct rendering with SkiaSharp. Core Layout Types DrawnUi.Maui offers several core layout types: SkiaLayout The base layout control for measurement, arrangement, and rendering of child elements. It supports different layout strategies via the LayoutType property: Managing child controls Performance optimizations (see below) <DrawUi:SkiaLayout LayoutType=\"Absolute\" WidthRequest=\"400\" HeightRequest=\"300\"> <!-- Child controls here --> </DrawUi:SkiaLayout> ContentLayout A specialized layout for hosting a single content element with optional overlays. <DrawUi:ContentLayout> <DrawUi:SkiaImage Source=\"background.png\" /> </DrawUi:ContentLayout> Layout Types The LayoutType property on SkiaLayout supports: Absolute: Free positioning using explicit coordinates Grid: Row and column-based layout Column: Vertical stacking (like VStack) Row: Horizontal stacking (like HStack) Wrap: Items wrap to new lines when space runs out <DrawUi:SkiaLayout LayoutType=\"Wrap\" Spacing=\"5,5\"> <DrawUi:SkiaLabel Text=\"Item 1\" /> <DrawUi:SkiaLabel Text=\"Item 2\" /> <!-- More items --> </DrawUi:SkiaLayout> Specialized Layout Controls InfiniteLayout Provides infinite scrolling in horizontal, vertical, or both directions. <DrawUi:InfiniteLayout ScrollOrientation=\"Both\"> <DrawUi:SkiaImage Source=\"large_map.png\" /> </DrawUi:InfiniteLayout> SnappingLayout Supports snap points for controlled scrolling, ideal for carousels or paginated interfaces. <DrawUi:SnappingLayout Orientation=\"Horizontal\"> <DrawUi:SkiaLabel Text=\"Page 1\" /> <DrawUi:SkiaLabel Text=\"Page 2\" /> <DrawUi:SkiaLabel Text=\"Page 3\" /> </DrawUi:SnappingLayout> Performance Optimization The layout system is designed for performance: Measurement Strategies Control how and when items are measured using the MeasureItemsStrategy property: MeasureFirst: Measures all items before rendering (good for static content) MeasureAll: Continuously measures all items (for dynamic content) MeasureVisible: Only measures visible items (most efficient for large collections) <DrawUi:SkiaLayout MeasureItemsStrategy=\"MeasureVisible\"> <!-- Child controls --> </DrawUi:SkiaLayout> Note: Data templating and collection binding (e.g., ItemsSource, ItemTemplate) are not currently available as direct properties in SkiaLayout. If you need dynamic content, you must add/remove child controls programmatically. Virtualization Virtualization is handled internally by the measurement strategy. When using MeasureVisible, only visible items are measured and rendered, improving performance for large collections. Example: Creating a Grid Layout <DrawUi:SkiaLayout LayoutType=\"Grid\" ColumnDefinitions=\"Auto,*,100\" RowDefinitions=\"Auto,*,50\"> <!-- Header spanning all columns --> <DrawUi:SkiaLabel Text=\"Grid Header\" Column=\"0\" ColumnSpan=\"3\" Row=\"0\" HorizontalOptions=\"Center\" /> <!-- Sidebar --> <DrawUi:SkiaLayout LayoutType=\"Column\" Column=\"0\" Row=\"1\" RowSpan=\"2\" BackgroundColor=\"LightGray\" Padding=\"10\"> <DrawUi:SkiaLabel Text=\"Menu Item 1\" /> <DrawUi:SkiaLabel Text=\"Menu Item 2\" /> <DrawUi:SkiaLabel Text=\"Menu Item 3\" /> </DrawUi:SkiaLayout> <!-- Main content --> <DrawUi:ContentLayout Column=\"1\" Row=\"1\"> <DrawUi:SkiaLabel Text=\"Main Content Area\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:ContentLayout> <!-- Right panel --> <DrawUi:SkiaLayout LayoutType=\"Column\" Column=\"2\" Row=\"1\" BackgroundColor=\"LightBlue\" Padding=\"5\"> <DrawUi:SkiaLabel Text=\"Panel Info\" /> </DrawUi:SkiaLayout> <!-- Footer spanning columns 1-2 --> <DrawUi:SkiaLabel Text=\"Footer\" Column=\"1\" ColumnSpan=\"2\" Row=\"2\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaLayout> All grid functionality is handled by SkiaLayout with LayoutType=\"Grid\". Under the Hood The layout system is built on top of the SkiaControl base class. Layout controls extend this for child management, measurement, and arrangement. Internally, structures like LayoutStructure and GridStructure efficiently track and manage layout information. Caching Caching options help balance CPU and memory usage: None: No caching, recalculated every frame Operations: Caches drawing commands Image: Caches as bitmap (more memory, less CPU) GPU: Uses hardware acceleration where available"}, "articles/controls/scroll.html": {"href": "articles/controls/scroll.html", "title": "Scroll Controls | DrawnUi Documentation", "summary": "Scroll Controls DrawnUi.Maui provides powerful scrolling containers that offer high-performance scrolling with advanced features like virtualization, infinite scrolling, and pull-to-refresh. This article covers the scroll controls available in the framework. SkiaScroll SkiaScroll is the core scrolling container in DrawnUi.Maui, providing smooth scrolling capabilities with physics-based animations and gesture handling. Basic Usage <DrawUi:SkiaScroll Orientation=\"Vertical\" WidthRequest=\"400\" HeightRequest=\"600\"> <DrawUi:SkiaLayout LayoutType=\"Column\" Spacing=\"10\"> <DrawUi:SkiaLabel Text=\"Item 1\" /> <DrawUi:SkiaLabel Text=\"Item 2\" /> <DrawUi:SkiaLabel Text=\"Item 3\" /> <!-- More items --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Multi-Directional Scrolling SkiaScroll supports scrolling in multiple directions: <DrawUi:SkiaScroll Orientation=\"Both\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <DrawUi:SkiaLayout HeightRequest=\"1500\" WidthRequest=\"1500\"> <!-- Content larger than viewport --> <DrawUi:SkiaImage Source=\"large_image.jpg\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Zoomable Content SkiaScroll supports pinch-to-zoom functionality: <DrawUi:SkiaScroll Orientation=\"Both\" ZoomLocked=\"False\" ZoomMin=\"1\" ZoomMax=\"3\"> <DrawUi:SkiaLayout> <DrawUi:SkiaImage Source=\"zoomable_image.jpg\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> The zoom properties control the behavior: ZoomLocked: When true, prevents zooming ZoomMin: Minimum zoom level (1.0 = original size) ZoomMax: Maximum zoom level Key Properties Property Type Description Orientation ScrollOrientation Direction of scrolling (Vertical, Horizontal, Both) Content SkiaControl The scrollable content Header SkiaControl Optional header element Footer SkiaControl Optional footer element HeaderSticky bool If true, header remains fixed during scrolling HeaderBehind bool If true, header appears behind scrollable content ViewportOffsetX float Horizontal scroll position ViewportOffsetY float Vertical scroll position UseVirtual bool Enables virtualization for large content ScrollWidthRequest float Width of the scrollable area ScrollHeightRequest float Height of the scrollable area EnableScrolling bool Enables/disables scrolling Scrolling Behavior Properties Property Type Description EnableMouseWheel bool Controls mouse wheel scrolling ScrollVelocityThreshold float Velocity threshold for scrolling ThresholdSwipeOnUp float Minimum velocity for fling animation SystemAnimationTimeSecs float Duration for system animations ParallaxOverscrollEnabled bool Enables parallax effect when overscrolling OverscrollEnabled bool Enables overscroll bouncing effect HeaderParallaxRatio float Controls parallax effect for header ScrollPositionParallaxRatio float Controls parallax effect for content CurrentSmoothScrollY float Current smooth scroll position (vertical) CurrentSmoothScrollX float Current smooth scroll position (horizontal) Headers and Footers SkiaScroll supports header and footer elements that can behave in special ways: <DrawUi:SkiaScroll HeaderSticky=\"True\" HeaderBehind=\"False\"> <DrawUi:SkiaScroll.Header> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"#3498DB\" HeightRequest=\"80\"> <DrawUi:SkiaLabel Text=\"Sticky Header\" TextColor=\"White\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" FontSize=\"18\" /> </DrawUi:SkiaShape> </DrawUi:SkiaScroll.Header> <DrawUi:SkiaLayout LayoutType=\"Column\" Spacing=\"10\"> <!-- Content items --> </DrawUi:SkiaLayout> <DrawUi:SkiaScroll.Footer> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"#2C3E50\" HeightRequest=\"60\"> <DrawUi:SkiaLabel Text=\"Footer\" TextColor=\"White\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> </DrawUi:SkiaScroll.Footer> </DrawUi:SkiaScroll> Scrolling to Position // Scroll to a specific position myScroll.ScrollToPosition(0, 500); // Scroll to Y = 500 // Scroll with animation myScroll.ScrollToPosition(0, 500, true); // Animated scroll // Scroll to an element myScroll.ScrollToView(targetElement, true); // Animated scroll to element Virtualization For large content sets, you can enable virtualization to improve performance: <DrawUi:SkiaScroll UseVirtual=\"True\" Orientation=\"Vertical\"> <DrawUi:SkiaLayout LayoutType=\"Column\" ItemsSource=\"{Binding LargeItemCollection}\" VirtualizationMode=\"Enabled\"> <DrawUi:SkiaLayout.ItemTemplate> <DataTemplate> <DrawUi:SkiaLabel Text=\"{Binding Title}\" /> </DataTemplate> </DrawUi:SkiaLayout.ItemTemplate> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Pull-to-Refresh SkiaScroll supports pull-to-refresh functionality: <DrawUi:SkiaScroll x:Name=\"MyScrollView\" Refreshing=\"OnRefreshing\"> <DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:RefreshIndicator /> </DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:SkiaLayout LayoutType=\"Column\"> <!-- Content items --> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> In code-behind: private async void OnRefreshing(object sender, EventArgs e) { // Perform refresh operation await LoadDataAsync(); // End refreshing state ((SkiaScroll)sender).EndRefresh(); } SkiaScrollLooped SkiaScrollLooped extends SkiaScroll to provide infinite, looped scrolling capabilities. This is perfect for carousels, banners, and other UI elements that should loop continuously. Basic Usage <DrawUi:SkiaScrollLooped Orientation=\"Horizontal\" WidthRequest=\"400\" HeightRequest=\"200\"> <DrawUi:SkiaLayout LayoutType=\"Row\" Spacing=\"10\"> <DrawUi:SkiaImage Source=\"image1.png\" WidthRequest=\"400\" HeightRequest=\"200\" /> <DrawUi:SkiaImage Source=\"image2.png\" WidthRequest=\"400\" HeightRequest=\"200\" /> <DrawUi:SkiaImage Source=\"image3.png\" WidthRequest=\"400\" HeightRequest=\"200\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScrollLooped> Key Properties Property Type Description IsBanner bool When true, behaves like a scrolling banner CycleSpace float Space between content cycles in pixels Banner Mode In banner mode, there's space between the end of one cycle and the beginning of the next: <DrawUi:SkiaScrollLooped Orientation=\"Horizontal\" IsBanner=\"True\" CycleSpace=\"100\" WidthRequest=\"400\" HeightRequest=\"200\"> <!-- Content that will repeat infinitely --> <DrawUi:SkiaLabel Text=\"Breaking News: DrawnUi.Maui Revolutionizes Cross-Platform UI Development\" FontSize=\"20\" TextColor=\"Red\" /> </DrawUi:SkiaScrollLooped> Current Index Tracking SkiaScrollLooped can track the current visible index: var scrollLooped = new SkiaScrollLooped { Orientation = ScrollOrientation.Horizontal, WidthRequest = 400, HeightRequest = 200 }; scrollLooped.CurrentIndexChanged += (s, index) => { Console.WriteLine($\"Current visible index: {index}\"); }; Advanced Usage Creating a Card Carousel <DrawUi:SkiaScrollLooped x:Name=\"Carousel\" Orientation=\"Horizontal\" WidthRequest=\"400\" HeightRequest=\"300\" SnapToChildren=\"Center\"> <DrawUi:SkiaLayout LayoutType=\"Row\" Spacing=\"20\" Padding=\"20,0\"> <!-- Card 1 --> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"16\" WidthRequest=\"300\" HeightRequest=\"250\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLayout LayoutType=\"Column\" Padding=\"20\"> <DrawUi:SkiaLabel Text=\"Card 1\" FontSize=\"24\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"Swipe to see more cards\" FontSize=\"16\" TextColor=\"#666666\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaShape> <!-- Card 2 --> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"16\" WidthRequest=\"300\" HeightRequest=\"250\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLayout LayoutType=\"Column\" Padding=\"20\"> <DrawUi:SkiaLabel Text=\"Card 2\" FontSize=\"24\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"Swipe to see more cards\" FontSize=\"16\" TextColor=\"#666666\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaShape> <!-- Card 3 --> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"16\" WidthRequest=\"300\" HeightRequest=\"250\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#40000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLayout LayoutType=\"Column\" Padding=\"20\"> <DrawUi:SkiaLabel Text=\"Card 3\" FontSize=\"24\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"Swipe to see more cards\" FontSize=\"16\" TextColor=\"#666666\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaShape> </DrawUi:SkiaLayout> </DrawUi:SkiaScrollLooped> Infinite Image Gallery <DrawUi:SkiaScrollLooped Orientation=\"Horizontal\" WidthRequest=\"400\" HeightRequest=\"400\" SnapToChildren=\"Center\"> <DrawUi:SkiaLayout LayoutType=\"Row\"> <DrawUi:SkiaImage Source=\"image1.jpg\" WidthRequest=\"400\" HeightRequest=\"400\" /> <DrawUi:SkiaImage Source=\"image2.jpg\" WidthRequest=\"400\" HeightRequest=\"400\" /> <DrawUi:SkiaImage Source=\"image3.jpg\" WidthRequest=\"400\" HeightRequest=\"400\" /> <DrawUi:SkiaImage Source=\"image4.jpg\" WidthRequest=\"400\" HeightRequest=\"400\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaScrollLooped> Building a Feed with Pull-to-Refresh <DrawUi:SkiaScroll x:Name=\"FeedScroll\" Refreshing=\"OnRefreshingFeed\"> <DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:RefreshIndicator /> </DrawUi:SkiaScroll.RefreshIndicator> <DrawUi:SkiaLayout LayoutType=\"Column\" Spacing=\"12\" Padding=\"16\" ItemsSource=\"{Binding FeedItems}\"> <DrawUi:SkiaLayout.ItemTemplate> <DataTemplate> <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"8\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#20000000\" BlurRadius=\"4\" Offset=\"0,2\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLayout LayoutType=\"Column\" Padding=\"16\"> <DrawUi:SkiaLabel Text=\"{Binding Title}\" FontSize=\"18\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"{Binding Description}\" FontSize=\"14\" TextColor=\"#666666\" /> </DrawUi:SkiaLayout> </DrawUi:SkiaShape> </DataTemplate> </DrawUi:SkiaLayout.ItemTemplate> </DrawUi:SkiaLayout> </DrawUi:SkiaScroll> Performance Considerations Virtualization For optimal performance with large datasets: Enable UseVirtual=\"True\" on SkiaScroll Use VirtualizationMode=\"Enabled\" on inner SkiaLayout Consider RecyclingTemplate=\"Enabled\" for template reuse Content Size When working with infinite scrolling: Monitor memory usage, especially with large images Use Cache=\"Operations\" for content that changes frequently For better performance with large collections, consider using data virtualization alongside UI virtualization Gestures If scroll gesture handling conflicts with other gesture recognizers: Adjust ScrollVelocityThreshold to control sensitivity For nested scrolling scenarios, ensure proper gesture propagation Consider using TouchScrollFriendly on inner components that need to receive touch events"}, "articles/controls/shapes.html": {"href": "articles/controls/shapes.html", "title": "SkiaShape | DrawnUi Documentation", "summary": "SkiaShape SkiaShape is a versatile control for rendering various geometric shapes in DrawnUi.Maui. Unlike traditional shape controls, SkiaShape offers high-performance rendering through SkiaSharp while supporting advanced features like custom paths, shadows, gradients, and content hosting. Basic Usage SkiaShape supports various shape types through its Type property: <DrawUi:SkiaShape Type=\"Rectangle\" WidthRequest=\"200\" HeightRequest=\"100\" BackgroundColor=\"Blue\" StrokeColor=\"White\" StrokeWidth=\"2\" CornerRadius=\"10\" /> Shape Types SkiaShape supports the following shape types: Rectangle: A basic rectangle, optionally with rounded corners Circle: A perfect circle that maintains 1:1 aspect ratio Ellipse: An oval shape that can have different width and height Path: A custom shape defined by SVG path data Polygon: A shape defined by a collection of points Line: A series of connected line segments Arc: A circular arc segment Common Properties Visual Properties Property Type Description BackgroundColor Color Fill color of the shape StrokeColor Color Outline color of the shape StrokeWidth float Width of the outline stroke CornerRadius float Rounded corner radius for rectangles StrokeCap StrokeCap End cap style for lines (Round, Butt, Square) StrokePath string Dash pattern for creating dashed lines StrokeBlendMode BlendMode Controls how strokes blend with underlying content ClipBackgroundColor bool If true, creates a \"hollow\" shape with just shadows and strokes Shape-Specific Properties Property Type Description PathData string SVG path data for Path type shapes Points Collection<SkiaPoint> Collection of points for Polygon or Line shapes SmoothPoints float Level of smoothing for Polygon/Line shapes (0.0-1.0) StartAngle float Starting angle for Arc shapes SweepAngle float Sweep angle for Arc shapes Advanced Features Shadow Effects SkiaShape supports multiple shadows through the Shadows collection property: <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"20\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#80000000\" BlurRadius=\"10\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> </DrawUi:SkiaShape> Gradients SkiaShape supports gradient fills via the BackgroundGradient and StrokeGradient properties: <DrawUi:SkiaShape Type=\"Rectangle\"> <DrawUi:SkiaShape.BackgroundGradient> <DrawUi:SkiaGradient Type=\"Linear\" StartColor=\"Red\" EndColor=\"Blue\" StartPoint=\"0,0\" EndPoint=\"1,1\" /> </DrawUi:SkiaShape.BackgroundGradient> </DrawUi:SkiaShape> Custom Paths For complex shapes, you can use SVG path data: <DrawUi:SkiaShape Type=\"Path\" PathData=\"M0,0L15.825,8.0 31.65,15.99 15.82,23.99 0,32 0,15.99z\" BackgroundColor=\"Red\" /> The PathData property follows standard SVG path notation: M: Move to (absolute) m: Move to (relative) L: Line to (absolute) l: Line to (relative) H/h: Horizontal line V/v: Vertical line C/c: Cubic bezier curve S/s: Smooth cubic bezier Q/q: Quadratic bezier curve T/t: Smooth quadratic bezier A/a: Elliptical arc Z/z: Close path As a Content Container SkiaShape can function as a container, clipping child elements to its shape boundaries: <DrawUi:SkiaShape Type=\"Circle\" BackgroundColor=\"Green\" WidthRequest=\"200\" HeightRequest=\"200\"> <DrawUi:SkiaImage Source=\"background.jpg\" VerticalOptions=\"Fill\" HorizontalOptions=\"Fill\" /> <DrawUi:SkiaLabel Text=\"Circular Content\" TextColor=\"White\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> The LayoutChildren property controls how children are arranged (Absolute, Column, Row, Grid). Creating Polygons For polygon shapes, you can define points in various ways: Using SkiaPoint Collection <DrawUi:SkiaShape Type=\"Polygon\" BackgroundColor=\"Purple\"> <DrawUi:SkiaShape.Points> <DrawUi:SkiaPoint X=\"0\" Y=\"0\" /> <DrawUi:SkiaPoint X=\"100\" Y=\"0\" /> <DrawUi:SkiaPoint X=\"100\" Y=\"100\" /> <DrawUi:SkiaPoint X=\"0\" Y=\"100\" /> </DrawUi:SkiaShape.Points> </DrawUi:SkiaShape> Using Relative Coordinates You can define points using relative coordinates (0.0-1.0) that automatically scale to the shape's dimensions: <DrawUi:SkiaShape Type=\"Polygon\" BackgroundColor=\"CornflowerBlue\"> <DrawUi:SkiaShape.Points> <DrawUi:SkiaPoint X=\"0.0\" Y=\"0.8\" /> <DrawUi:SkiaPoint X=\"0.0\" Y=\"0.7\" /> <DrawUi:SkiaPoint X=\"1.0\" Y=\"0.2\" /> <DrawUi:SkiaPoint X=\"1.0\" Y=\"0.3\" /> </DrawUi:SkiaShape.Points> </DrawUi:SkiaShape> Using String Definition You can also use a converter for inline point definitions: <DrawUi:SkiaShape Type=\"Polygon\" BackgroundColor=\"Purple\" Points=\"0,0 100,0 100,100 0,100\" /> Predefined Shapes SkiaShape provides predefined point collections for common shapes: <DrawUi:SkiaShape Type=\"Polygon\" BackgroundColor=\"Yellow\" Points=\"{x:Static DrawUi:SkiaShape.PolygonStar}\" /> Smooth Curves For smoother, curved polygons, adjust the SmoothPoints property (0.0-1.0): <DrawUi:SkiaShape Type=\"Polygon\" BackgroundColor=\"#220000FF\" SmoothPoints=\"0.9\" Points=\"0.0,0.8 0.0,0.7 1.0,0.2 1.0,0.3\" /> A value of 0 creates sharp corners, while a value of 1.0 creates maximally smooth curves. Creating Lines Lines can be created using the same point collection approach: <DrawUi:SkiaShape Type=\"Line\" StrokeColor=\"Black\" StrokeWidth=\"2\" Points=\"0,0 50,50 100,0 150,50\" /> Customize line appearance with: StrokeCap: Controls how line ends appear StrokePath: Define dash patterns (\"5,5\" creates 5px dashes with 5px gaps) Practical Examples Card with Shadow <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"12\" Padding=\"16\" WidthRequest=\"300\" HeightRequest=\"150\" LayoutChildren=\"Column\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#22000000\" BlurRadius=\"20\" Offset=\"0,4\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLabel Text=\"Card Title\" FontSize=\"18\" FontWeight=\"Bold\" /> <DrawUi:SkiaLabel Text=\"This is a card with rounded corners and a shadow effect. SkiaShape makes it easy to create modern UI components.\" TextColor=\"#666666\" Margin=\"0,10,0,0\" /> </DrawUi:SkiaShape> Progress Indicator <DrawUi:SkiaShape Type=\"Arc\" StrokeColor=\"#EEEEEE\" StrokeWidth=\"10\" BackgroundColor=\"Transparent\" StartAngle=\"0\" SweepAngle=\"360\" WidthRequest=\"100\" HeightRequest=\"100\"> <DrawUi:SkiaShape Type=\"Arc\" StrokeColor=\"Blue\" StrokeWidth=\"10\" BackgroundColor=\"Transparent\" StartAngle=\"0\" SweepAngle=\"{Binding Progress}\" WidthRequest=\"100\" HeightRequest=\"100\" /> <DrawUi:SkiaLabel Text=\"{Binding ProgressText}\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> Custom Button <DrawUi:SkiaShape Type=\"Path\" PathData=\"M10,0 L90,0 C95,0 100,5 100,10 L100,40 C100,45 95,50 90,50 L10,50 C5,50 0,45 0,40 L0,10 C0,5 5,0 10,0 Z\" BackgroundColor=\"Blue\" WidthRequest=\"100\" HeightRequest=\"50\"> <DrawUi:SkiaShape.GestureRecognizers> <TapGestureRecognizer Command=\"{Binding ButtonCommand}\" /> </DrawUi:SkiaShape.GestureRecognizers> <DrawUi:SkiaLabel Text=\"SUBMIT\" TextColor=\"White\" FontWeight=\"Bold\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </DrawUi:SkiaShape> Performance Considerations For static shapes, set Cache=\"Image\" to render once and cache as bitmap For frequently animated shapes, use Cache=\"Operations\" for best performance Avoid excessive shadows or complex paths in performance-critical UI For very complex paths, pre-process SVG data when possible rather than computing at runtime Platform Specific Notes SkiaShape renders consistently across all platforms supported by MAUI, ensuring that your UI maintains the same appearance on Android, iOS, Windows, and macOS."}, "articles/controls/shell.html": {"href": "articles/controls/shell.html", "title": "SkiaShell | DrawnUi Documentation", "summary": "SkiaShell SkiaShell is a powerful navigation framework for DrawnUi applications that provides full navigation capabilities similar to MAUI's Shell, but with the performance and customization benefits of direct SkiaSharp rendering. Overview SkiaShell acts as a replacement for the standard MAUI Shell, allowing for fully drawn UI with SkiaSharp while maintaining compatibility with MAUI's routing capabilities. It provides complete navigation stack management, modal presentations, popups, and toast notifications within a DrawnUi.Maui Canvas. Key Features MAUI-compatible navigation: Use familiar navigation patterns with GoToAsync Navigation stack management: Handle screen, modal, popup, and toast stacks Routing with parameters: Support for query parameters in navigation routes Modal and popup systems: Present overlays with customizable animations Background freezing: Capture and display screenshots of current views as backgrounds Toast notifications: Show temporary messages with automatic dismissal Back button handling: Handle hardware back button with customizable behavior Setup Basic Configuration To use SkiaShell in your application, you need to: Optiinal: create a page that derives from DrawnUiBasePage. This class provide support to track native keyboard to be able to adapt layout accordingly. Add a Canvas to your page Set up the required layout structure on the canvas Initialize the shell to register elements present on the canvas that would serve for navigation Here's a basic example: <drawn:DrawnUiBasePage x:Class=\"MyApp.MainShellPage\" xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\" xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\" xmlns:drawn=\"clr-namespace:DrawnUi.Maui;assembly=DrawnUi.Maui\"> <drawn:Canvas x:Name=\"MainCanvas\" HardwareAcceleration=\"Enabled\" Gestures=\"Enabled\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <!-- Main content goes here --> <drawn:SkiaLayout Tag=\"ShellLayout\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <drawn:SkiaLayout Tag=\"RootLayout\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <drawn:SkiaViewSwitcher Tag=\"NavigationLayout\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\" /> </drawn:SkiaLayout> </drawn:SkiaLayout> </drawn:Canvas> </drawn:DrawnUiBasePage> In your code-behind: public partial class MainShellPage : DrawnUiBasePage { public MainShellPage() { InitializeComponent(); // Initialize and register the shell Shell = new SkiaShell(); Shell.Initialize(MainCanvas); } public SkiaShell Shell { get; private set; } // Register routes in OnAppearing or constructor protected override void OnAppearing() { base.OnAppearing(); // Register navigation routes Shell.RegisterRoute(\"home\", typeof(HomePage)); Shell.RegisterRoute(\"details\", typeof(DetailsPage)); // Navigate to the initial route Shell.GoToAsync(\"home\"); } } Required Layout Tags SkiaShell relies on specific tags to identify key components in your layout: ShellLayout: The outer container for all navigation elements (typically directly inside the Canvas) RootLayout: The main layout container (inside ShellLayout) NavigationLayout: A SkiaViewSwitcher that handles page transitions (inside RootLayout) Navigation Basic Navigation // Navigate to a registered route await Shell.GoToAsync(\"details\"); // Navigate with parameters await Shell.GoToAsync(\"details?id=123&name=Product\"); // Navigate back bool handled = Shell.GoBack(true); // true to animate // Check if can go back bool canGoBack = Shell.CanGoBack(); Push and Pop Pages // Push a page instance var detailsPage = new DetailsPage(); await Shell.PushAsync(detailsPage, animated: true); // Pop the current page var poppedPage = await Shell.PopAsync(animated: true); // Pop to the root page await Shell.PopToRootAsync(animated: true); Route Registration Routes need to be registered before navigation: // Register a route with a page type Shell.RegisterRoute(\"details\", typeof(DetailsPage)); // Register a route with a factory function Shell.RegisterRoute(\"profile\", () => new ProfilePage()); Route Parameters Extract parameters in the destination page: public class DetailsPage : SkiaControl { protected override void OnParentChanged() { base.OnParentChanged(); // Get query parameters from shell route var shell = AppShell; // Helper property to get the shell if (shell?.RouteParameters != null) { string id = shell.RouteParameters.GetValueOrDefault(\"id\"); string name = shell.RouteParameters.GetValueOrDefault(\"name\"); // Use the parameters LoadDetails(id, name); } } } Modals and Popups Modal Presentation // Show a modal from a registered route await Shell.PushModalAsync(\"details\", useGestures: true, animated: true); // Show a modal from a page instance await Shell.PushModalAsync(new DetailsPage(), useGestures: true, animated: true); // Dismiss the modal await Shell.PopModalAsync(animated: true); Popup Presentation // Create a popup content var popupContent = new SkiaLayout { WidthRequest = 300, HeightRequest = 200, BackgroundColor = Colors.White, CornerRadius = 10 }; // Add content to the popup popupContent.Add(new SkiaLabel { Text = \"This is a popup\", HorizontalOptions = LayoutOptions.Center, VerticalOptions = LayoutOptions.Center }); // Show popup await Shell.OpenPopupAsync( content: popupContent, animated: true, closeWhenBackgroundTapped: true, freezeBackground: true ); // Close popup await Shell.ClosePopupAsync(animated: true); Toast Notifications // Show a simple text toast Shell.ShowToast(\"Operation completed successfully\", msShowTime: 3000); // Show a custom toast Shell.ShowToast(new SkiaMarkdownLabel { Text = \"**Important:** Your data has been saved.\", TextColor = Colors.White }, msShowTime: 3000); Customization Visual Customization // Set global appearance properties SkiaShell.PopupBackgroundColor = new SKColor(0, 0, 0, 128); // 50% transparent black SkiaShell.PopupsBackgroundBlur = 10; // Blur amount SkiaShell.PopupsAnimationSpeed = 350; // Animation duration in ms SkiaShell.ToastBackgroundColor = new SKColor(50, 50, 50, 230); SkiaShell.ToastTextColor = Colors.White; Animation Control Control the animation duration and timing: // Fast navigation with minimal animation await Shell.GoToAsync(\"details\", new NavigationParameters { AnimationDuration = 150 }); // Slow modal presentation with specific animation await Shell.PushModalAsync(\"settings\", new NavigationParameters { AnimationDuration = 500, AnimationType = NavigationType.SlideFromRight }); Navigation Events // Subscribe to navigation events Shell.Navigated += OnNavigated; Shell.Navigating += OnNavigating; Shell.RouteChanged += OnRouteChanged; // Handle the events private void OnNavigating(object sender, SkiaShellNavigatingArgs e) { // Access navigation details string source = e.Source.ToString(); string destination = e.Destination; // Optionally cancel navigation if (HasUnsavedChanges) { e.Cancel = true; ShowSavePrompt(); } } private void OnNavigated(object sender, SkiaShellNavigatedArgs e) { // Navigation completed Debug.WriteLine($\"Navigated from {e.Source} to {e.Destination}\"); } Custom Back Navigation Implement the IHandleGoBack interface to handle back navigation in view models: public class EditViewModel : IHandleGoBack { public bool OnShellGoBack(bool animate) { // Check for unsaved changes if (HasUnsavedChanges) { // Show confirmation dialog ShowConfirmationDialog(); // Return true to indicate we're handling the back navigation return true; } // Return false to let the default back navigation occur return false; } } Advanced Features Background Freezing When showing modals or popups, SkiaShell can freeze the background content by taking a screenshot: // Show a modal with frozen background await Shell.PushModalAsync(\"details\", new NavigationParameters { FreezeBackground = true, FreezeBlur = 5, FreezeTint = new SKColor(0, 0, 0, 100) }); Custom Modal Presentation Create a custom modal presentation style: // Subclass SkiaShell to customize modal presentation public class CustomShell : SkiaShell { protected override SkiaDrawer CreateModalDrawer(SkiaControl content, bool useGestures) { var drawer = base.CreateModalDrawer(content, useGestures); // Customize the drawer drawer.Direction = DrawerDirection.FromBottom; drawer.HeaderSize = 40; // Add custom styling content.BackgroundColor = Colors.White; content.CornerRadius = new CornerRadius(20, 20, 0, 0); return drawer; } } Handling Page Lifecycle Implement navigation-aware controls: public class MyPage : SkiaLayout, INavigationAware { public void OnAppearing() { // Page is becoming visible LoadData(); } public void OnDisappearing() { // Page is being hidden SaveData(); } } Example: Complete Shell Application Here's a complete example of a minimal shell-based application: <!-- MainShell.xaml --> <drawn:DrawnUiBasePage x:Class=\"MyApp.MainShell\" xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\" xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\" xmlns:drawn=\"clr-namespace:DrawnUi.Maui;assembly=DrawnUi.Maui\"> <drawn:Canvas x:Name=\"MainCanvas\" HardwareAcceleration=\"Enabled\" Gestures=\"Enabled\"> <drawn:SkiaLayout Tag=\"ShellLayout\" BackgroundColor=\"#F0F0F0\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <drawn:SkiaLayout Tag=\"RootLayout\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <!-- Navigation content --> <drawn:SkiaViewSwitcher Tag=\"NavigationLayout\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\" TransitionType=\"SlideHorizontal\" /> <!-- Bottom tabs --> <drawn:SkiaLayout LayoutType=\"Row\" HeightRequest=\"60\" BackgroundColor=\"White\" VerticalOptions=\"End\" HorizontalOptions=\"Fill\" Spacing=\"0\"> <drawn:SkiaHotspot HorizontalOptions=\"FillAndExpand\" Tapped=\"OnHomeTabTapped\"> <drawn:SkiaLabel Text=\"Home\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </drawn:SkiaHotspot> <drawn:SkiaHotspot HorizontalOptions=\"FillAndExpand\" Tapped=\"OnProfileTabTapped\"> <drawn:SkiaLabel Text=\"Profile\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </drawn:SkiaHotspot> <drawn:SkiaHotspot HorizontalOptions=\"FillAndExpand\" Tapped=\"OnSettingsTabTapped\"> <drawn:SkiaLabel Text=\"Settings\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> </drawn:SkiaHotspot> </drawn:SkiaLayout> </drawn:SkiaLayout> </drawn:SkiaLayout> </drawn:Canvas> </drawn:DrawnUiBasePage> // MainShell.xaml.cs public partial class MainShell : DrawnUiBasePage { public SkiaShell Shell { get; private set; } public MainShell() { InitializeComponent(); // Initialize shell Shell = new SkiaShell(); Shell.Initialize(MainCanvas); // Register routes Shell.RegisterRoute(\"home\", typeof(HomePage)); Shell.RegisterRoute(\"profile\", typeof(ProfilePage)); Shell.RegisterRoute(\"settings\", typeof(SettingsPage)); Shell.RegisterRoute(\"details\", typeof(DetailsPage)); // Navigate to initial route Shell.GoToAsync(\"home\"); } private void OnHomeTabTapped(object sender, EventArgs e) { Shell.GoToAsync(\"home\"); } private void OnProfileTabTapped(object sender, EventArgs e) { Shell.GoToAsync(\"profile\"); } private void OnSettingsTabTapped(object sender, EventArgs e) { Shell.GoToAsync(\"settings\"); } protected override bool OnBackButtonPressed() { // Let shell handle back button return Shell.GoBack(true); } } Performance Considerations Layer Management: SkiaShell maintains separate navigation stacks for better organization and performance Z-Index Control: Different types of content (modals, popups, toasts) have different Z-index ranges Animation Control: Customize animations or disable them for better performance Background Freezing: Uses screenshots to avoid continuously rendering background content Locking Mechanism: Uses semaphores to prevent multiple simultaneous navigation operations"}, "articles/controls/sprites.html": {"href": "articles/controls/sprites.html", "title": "Sprite Controls | DrawnUi Documentation", "summary": "Sprite Controls DrawnUi provides specialized controls for rendering sprite-based animations. This article covers the sprite animation components available in the framework. SkiaSprite SkiaSprite is a high-performance control for displaying and animating sprite sheets. It loads sprite sheets (a single image containing multiple animation frames arranged in a grid) and renders individual frames with precise timing for smooth animations. Basic Usage <draw:SkiaSprite Source=\"sprites/explosion.png\" Columns=\"8\" Rows=\"4\" FramesPerSecond=\"24\" AutoPlay=\"True\" Repeat=\"-1\" WidthRequest=\"128\" HeightRequest=\"128\" /> Key Properties Property Type Description Source string Path or URL of the sprite sheet image Columns int Number of columns in the sprite sheet grid Rows int Number of rows in the sprite sheet grid FramesPerSecond int Animation speed in frames per second (default: 24) MaxFrames int Maximum number of frames to use (0 means use all) CurrentFrame int Current frame being displayed (0-based index) FrameSequence int[] Custom sequence of frames to play AnimationName string Name of a predefined animation sequence AutoPlay bool Whether animation starts automatically when loaded Repeat int Number of times to repeat (-1 for infinite) SpeedRatio double Adjusts animation speed (1.0 is normal speed) DefaultFrame int Frame to display when not playing Animation Control Control playback programmatically: // Start animation mySprite.Start(); // Stop animation mySprite.Stop(); // Jump to a specific frame mySprite.CurrentFrame = 5; // Seek to a time position mySprite.Seek(timeInMs); Animation Events // Animation started event mySprite.Started += (sender, e) => { // Animation has started }; // Animation completed event (fires after all repeats) mySprite.Finished += (sender, e) => { // Animation has finished }; Sprite Sheet Structure A sprite sheet is a single image containing multiple frames arranged in a grid: +---+---+---+---+ | 0 | 1 | 2 | 3 | +---+---+---+---+ | 4 | 5 | 6 | 7 | +---+---+---+---+ | 8 | 9 | 10| 11| +---+---+---+---+ The Columns and Rows properties define the grid structure: In the example above, set Columns=\"4\" and Rows=\"3\" Frames are numbered left-to-right, top-to-bottom (0 to 11) Each frame must have the same dimensions Loading Sprite Sheets SkiaSprite supports loading sprite sheets from various sources: <!-- From app resources --> <draw:SkiaSprite Source=\"running_character.png\" /> <!-- From local file --> <draw:SkiaSprite Source=\"file:///path/to/animation.png\" /> <!-- From URL --> <draw:SkiaSprite Source=\"https://example.com/sprites/animation.png\" /> Creating a Character Animation <draw:SkiaLayout WidthRequest=\"200\" HeightRequest=\"200\" BackgroundColor=\"#F0F0F0\"> <draw:SkiaSprite x:Name=\"PlayerAnimation\" Source=\"character_run.png\" Columns=\"8\" Rows=\"1\" FramesPerSecond=\"12\" AutoPlay=\"False\" WidthRequest=\"128\" HeightRequest=\"128\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> <draw:SkiaButton Text=\"Run\" WidthRequest=\"80\" HeightRequest=\"40\" Margin=\"0,140,0,0\" HorizontalOptions=\"Center\" Tapped=\"OnRunButtonTapped\" /> </draw:SkiaLayout> In code-behind: private void OnRunButtonTapped(object sender, EventArgs e) { if (PlayerAnimation.IsPlaying) { PlayerAnimation.Stop(); } else { PlayerAnimation.Start(); } } Animation States Use the DefaultFrame property to control which frame is shown when the animation is not playing: <!-- Show first frame when not playing --> <draw:SkiaSprite Source=\"button_press.png\" Columns=\"10\" Rows=\"1\" DefaultFrame=\"0\" /> <!-- Show last frame when not playing (useful for transitions that should remain in end state) --> <draw:SkiaSprite Source=\"door_open.png\" Columns=\"8\" Rows=\"1\" DefaultFrame=\"7\" /> Advanced: Frame Sequences and Reusing Spritesheets One of the key features of SkiaSprite is the ability to create multiple animations from a single spritesheet by defining frame sequences: Using Frame Sequences Directly <!-- Manual frame sequence in XAML using array converter --> <draw:SkiaSprite Source=\"character.png\" Columns=\"8\" Rows=\"2\" FrameSequence=\"{Binding FrameSequence, Converter={StaticResource IntArrayConverter}}\" FramesPerSecond=\"12\" /> In code-behind: // Define a specific frame sequence mySprite.FrameSequence = new[] { 3, 4, 5, 4, 3 }; // Play frames in this exact order Creating Reusable Named Animations Register animations once at application startup: // In your App.xaml.cs or similar initialization code protected override void OnStart() { base.OnStart(); // Register named animations for a character spritesheet SkiaSprite.CreateAnimationSequence(\"Idle\", new[] { 0, 1, 2, 1 }); SkiaSprite.CreateAnimationSequence(\"Walk\", new[] { 3, 4, 5, 6, 7, 8 }); SkiaSprite.CreateAnimationSequence(\"Jump\", new[] { 9, 10, 11 }); SkiaSprite.CreateAnimationSequence(\"Attack\", new[] { 12, 13, 14, 15 }); } Then in XAML just reference by name: <!-- Multiple sprites sharing the same spritesheet with different animations --> <draw:SkiaSprite Source=\"character.png\" AnimationName=\"Walk\" /> <draw:SkiaSprite Source=\"character.png\" AnimationName=\"Attack\" /> Or switch animations in code: // Change the animation based on character state void UpdateCharacterState(PlayerState state) { switch (state) { case PlayerState.Idle: characterSprite.AnimationName = \"Idle\"; break; case PlayerState.Walking: characterSprite.AnimationName = \"Walk\"; break; case PlayerState.Jumping: characterSprite.AnimationName = \"Jump\"; break; case PlayerState.Attacking: characterSprite.AnimationName = \"Attack\"; break; } } Memory Management and Caching SkiaSprite includes an intelligent caching system to avoid reloading the same spritesheets multiple times: // Clear the entire spritesheet cache SkiaSprite.ClearCache(); // Remove a specific spritesheet from cache SkiaSprite.RemoveFromCache(\"character.png\"); The control automatically handles: Caching spritesheets in memory when first loaded Sharing the same bitmap instance between multiple SkiaSprite controls Safe disposal when controls are no longer used Advanced: Custom Animation Speed Adjust animation speed using SpeedRatio: <!-- Half speed --> <draw:SkiaSprite Source=\"walking.png\" Columns=\"8\" Rows=\"1\" SpeedRatio=\"0.5\" /> <!-- Double speed --> <draw:SkiaSprite Source=\"running.png\" Columns=\"8\" Rows=\"1\" SpeedRatio=\"2.0\" /> Example: Button with Animated States <draw:SkiaShape Type=\"Rectangle\" CornerRadius=\"8\" BackgroundColor=\"#3498DB\" WidthRequest=\"200\" HeightRequest=\"60\"> <draw:SkiaHotspot Tapped=\"OnButtonTapped\"> <draw:SkiaLayout HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <!-- Button text --> <draw:SkiaLabel Text=\"Click Me\" TextColor=\"White\" FontSize=\"18\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> <!-- Button animation that plays on tap --> <draw:SkiaSprite x:Name=\"ButtonAnimation\" Source=\"button_press.png\" Columns=\"5\" Rows=\"1\" FramesPerSecond=\"30\" AutoPlay=\"False\" Repeat=\"0\" HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\" Opacity=\"0.5\" /> </draw:SkiaLayout> </draw:SkiaHotspot> </draw:SkiaShape> In code-behind: private void OnButtonTapped(object sender, EventArgs e) { ButtonAnimation.Stop(); ButtonAnimation.CurrentFrame = 0; ButtonAnimation.Start(); } Performance Considerations Memory Management Sprite sheets are cached automatically to avoid redundant loading For large or numerous sprite sheets, consider monitoring memory usage Use ClearCache() or RemoveFromCache() when spritesheets are no longer needed Optimization Tips Sprite Sheet Size Keep sprite sheets as small as possible while maintaining required quality Consider using sprite packing algorithms to maximize space efficiency Use power-of-two dimensions for better GPU compatibility Frame Rate Choose an appropriate FramesPerSecond value for your animation For simple character animations, 12-15 FPS is often sufficient For smoother animations, 24-30 FPS provides better results Higher frame rates consume more resources Frame Sequences For complex animations, use frame sequences to avoid redundant frames Share spritesheets between multiple sprites using the built-in caching Image Format Use PNG for sprite sheets with transparency Consider WebP for better compression if supported Optimize image file size using appropriate compression tools Implementation Notes The SkiaSprite control derives from AnimatedFramesRenderer, which provides the base functionality for frame-based animation. The control internally: Loads a spritesheet image into an SKBitmap Calculates frame dimensions based on Columns and Rows Extracts individual frames on demand by creating a new bitmap for each frame Uses a SkiaImage control to display the current frame Manages animation timing through the inherited animator functionality This architecture aligns with other animation controls in DrawnUi like SkiaGif and SkiaLottie."}, "articles/controls/switches.html": {"href": "articles/controls/switches.html", "title": "Switches and Toggles | DrawnUi Documentation", "summary": "Switches and Toggles DrawnUi provides toggle controls with platform-specific styling, including switches and checkboxes. SkiaSwitch SkiaSwitch is a toggle control styled according to platform conventions, similar to an on/off switch. Basic Usage <draw:SkiaSwitch IsToggled=\"false\" WidthRequest=\"50\" HeightRequest=\"30\" ColorFrameOff=\"Gray\" ColorFrameOn=\"Green\" ColorThumbOff=\"White\" ColorThumbOn=\"White\" Toggled=\"OnSwitchToggled\" /> Platform-Specific Styling Set the ControlStyle property to apply platform-specific styling: Platform: Automatically selects the appropriate style for the current platform Cupertino: iOS-style switch with pill-shaped track Material: Android Material Design switch Windows: Windows-style switch <draw:SkiaSwitch ControlStyle=\"Cupertino\" IsToggled=\"true\" /> Properties Property Type Description IsToggled bool Whether the switch is toggled on or off ColorFrameOn Color The color of the track when toggled on ColorFrameOff Color The color of the track when toggled off ColorThumbOn Color The color of the thumb when toggled on ColorThumbOff Color The color of the thumb when toggled off ControlStyle PrebuiltControlStyle The platform-specific style IsAnimated bool Whether state changes are animated Events Toggled: Raised when the switch is toggled on or off SkiaCheckbox SkiaCheckbox is a toggle control styled as a checkbox with platform-specific appearance. Basic Usage <draw:SkiaCheckbox IsToggled=\"false\" WidthRequest=\"24\" HeightRequest=\"24\" ColorFrameOff=\"Gray\" ColorFrameOn=\"Blue\" ColorThumbOff=\"Transparent\" ColorThumbOn=\"White\" Toggled=\"OnCheckboxToggled\" /> Platform-Specific Styling Like SkiaSwitch, SkiaCheckbox supports platform-specific styling through the ControlStyle property. Properties SkiaCheckbox shares most properties with SkiaSwitch, both inheriting from SkiaToggle. SkiaToggle SkiaToggle is the base class for toggle controls. You can use it to create custom toggle controls with similar behavior to switches and checkboxes. Key Properties Property Type Description IsToggled bool Whether the control is toggled on or off DefaultValue bool The default toggle state ColorFrameOn/Off Color The color of the frame in each state ColorThumbOn/Off Color The color of the thumb in each state IsAnimated bool Whether state changes are animated Events Toggled: Raised when the toggle state changes"}, "articles/controls/text.html": {"href": "articles/controls/text.html", "title": "Text Controls | DrawnUi Documentation", "summary": "Text Controls DrawnUi.Maui offers powerful text rendering capabilities through its specialized text controls. These controls provide high-performance text rendering with advanced formatting options while maintaining consistent appearance across all platforms. SkiaLabel SkiaLabel is the primary text rendering control in DrawnUi.Maui, rendering text directly with SkiaSharp. Unlike traditional MAUI labels, SkiaLabel provides pixel-perfect text rendering with advanced formatting capabilities. Basic Usage <DrawUi:SkiaLabel Text=\"Hello World\" TextColor=\"Black\" FontSize=\"18\" HorizontalTextAlignment=\"Center\" VerticalTextAlignment=\"Center\" /> Key Properties Property Type Description Text string The text content to display TextColor Color Text color FontFamily string Font family name FontSize float Font size in logical pixels FontWeight int Font weight (100-900 scale, 400=normal, 700=bold) FontAttributes FontAttributes Bold/Italic/None HorizontalTextAlignment DrawTextAlignment Text horizontal alignment (Start, Center, End, Fill) VerticalTextAlignment DrawTextAlignment Text vertical alignment (Start, Center, End) LineBreakMode LineBreakMode How text should wrap or truncate MaxLines int Maximum number of lines to display StrokeColor Color Outline color StrokeWidth double Outline width DropShadowColor Color Shadow color DropShadowSize double Shadow blur radius DropShadowOffsetX/DropShadowOffsetY double Shadow offset AutoSize AutoSizeType Auto-sizing mode AutoSizeText string Text to use for auto-sizing calculations LineSpacing double Line spacing multiplier ParagraphSpacing double Paragraph spacing multiplier CharacterSpacing double Character spacing multiplier IsMonospaced bool Enables monospaced text rendering MonoForDigits string Use mono width for digits (e.g. \"8\") Rich Text Formatting (Spans) SkiaLabel supports rich text formatting through its Spans collection: <DrawUi:SkiaLabel> <DrawUi:SkiaLabel.Spans> <DrawUi:TextSpan Text=\"Hello \" TextColor=\"Black\" FontSize=\"18\" /> <DrawUi:TextSpan Text=\"Beautiful \" TextColor=\"Red\" FontSize=\"20\" FontWeight=\"700\" /> <DrawUi:TextSpan Text=\"World!\" TextColor=\"Blue\" FontSize=\"18\" FontAttributes=\"Italic\" /> </DrawUi:SkiaLabel.Spans> </DrawUi:SkiaLabel> Interactive Spans You can make any text span interactive by adding the Tapped event handler: <DrawUi:SkiaLabel FontSize=\"15\" LineSpacing=\"1.5\" TextColor=\"Black\"> <DrawUi:TextSpan Text=\"Regular text \" /> <DrawUi:TextSpan Text=\"tappable link\" TextColor=\"Purple\" Tapped=\"OnSpanTapped\" Tag=\"link-id\" Underline=\"True\" /> <DrawUi:TextSpan Text=\" more text...\" /> </DrawUi:SkiaLabel> In your code-behind: private void OnSpanTapped(object sender, EventArgs e) { var span = sender as TextSpan; string tag = span?.Tag?.ToString(); // Handle the tap event based on the span or its tag } Styling Spans TextSpan supports various styling options: <DrawUi:TextSpan Text=\"Bold text\" FontAttributes=\"Bold\" /> <DrawUi:TextSpan Text=\"Italic text\" FontAttributes=\"Italic\" /> <DrawUi:TextSpan Text=\"Underlined text\" Underline=\"True\" /> <DrawUi:TextSpan Text=\"Strikethrough text\" Strikeout=\"True\" /> <DrawUi:TextSpan Text=\"Highlighted text\" BackgroundColor=\"Yellow\" /> Emoji Support For emoji rendering, use the AutoFont property: <DrawUi:TextSpan Text=\"Regular text \" /> <DrawUi:TextSpan AutoFont=\"True\" Text=\"🌐🚒🙎🏽👻🤖\" /> <DrawUi:TextSpan Text=\" more text...\" /> This ensures proper emoji rendering by finding and using appropriate fonts. Text Effects SkiaLabel supports various text effects: Drop Shadow Use the following properties for shadow effects: DropShadowColor: Shadow color DropShadowSize: Blur radius DropShadowOffsetX, DropShadowOffsetY: Shadow offset <DrawUi:SkiaLabel Text=\"Shadowed Text\" FontSize=\"24\" TextColor=\"White\" DropShadowColor=\"#80000000\" DropShadowSize=\"3\" DropShadowOffsetX=\"1\" DropShadowOffsetY=\"1\" /> Outlined Text <DrawUi:SkiaLabel Text=\"Outlined Text\" FontSize=\"24\" TextColor=\"White\" StrokeColor=\"Black\" StrokeWidth=\"1\" /> Gradient Text <DrawUi:SkiaLabel Text=\"Gradient Text\" FontSize=\"24\" FillGradient=\"{StaticResource MyGradient}\" /> Auto-sizing Text SkiaLabel features powerful automatic font sizing capabilities that can dynamically adjust text to fit your container: <DrawUi:SkiaLabel Text=\"This text will resize to fit the available space\" AutoSize=\"TextToView\" FontSize=\"24\" MaxLines=\"1\" /> AutoSize: Controls auto-sizing mode (None, TextToWidth, TextToHeight, TextToView) AutoSizeText: Text to use for sizing calculations Monospaced Text Rendering SkiaLabel provides the ability to render text in a monospaced style, regardless of the font used: <DrawUi:SkiaLabel Text=\"This text will be monospaced\" FontSize=\"18\" MonoForDigits=\"8\" /> MonoForDigits: Use mono width for digits (e.g. \"8\") Performance Considerations For static text, set Cache=\"Image\" to render once and cache as bitmap For frequently updated text, use Cache=\"Operations\" for best performance Consider setting MaxLines when appropriate to avoid unnecessary layout calculations For large blocks of text, monitor performance and consider breaking into multiple labels Use monospaced features only when needed as it adds some calculation overhead For complex shadow effects, consider using Cache=\"Image\" to optimize rendering SkiaMarkdownLabel SkiaMarkdownLabel extends SkiaLabel to provide Markdown formatting capabilities. It parses Markdown syntax and renders properly formatted text. Basic Usage <DrawUi:SkiaMarkdownLabel> # Markdown Title This is a paragraph with **bold** and *italic* text. - List item 1 - List item 2 [Visit Documentation](https://link.example.com) `Inline code` looks like this. ```csharp // Code block var label = new SkiaMarkdownLabel(); </DrawUi:SkiaMarkdownLabel> ### Supported Markdown Features - **Headings** (# H1, ## H2) - **Text formatting** (bold, italic, strikethrough) - **Lists** (bulleted and numbered) - **Links** (with customizable styling) - **Code** (inline and blocks) - **Paragraphs** (with proper spacing) ### Customizing Markdown Style ```xml <DrawUi:SkiaMarkdownLabel LinkColor=\"Blue\" CodeTextColor=\"DarkGreen\" CodeBackgroundColor=\"#EEEEEE\" CodeBlockBackgroundColor=\"#F5F5F5\" StrikeoutColor=\"Red\" PrefixBullet=\"• \" PrefixNumbered=\"{0}. \" UnderlineLink=\"True\" UnderlineWidth=\"1\"> # Custom Styled Markdown This has **custom** styling for [links](https://example.com) and `code blocks`. </DrawUi:SkiaMarkdownLabel> Link Handling SkiaMarkdownLabel provides built-in support for handling link taps: <DrawUi:SkiaMarkdownLabel LinkTapped=\"OnLinkTapped\" CommandLinkTapped=\"{Binding OpenLinkCommand}\"> Check out [this link](https://example.com)! </DrawUi:SkiaMarkdownLabel> In your code-behind: private void OnLinkTapped(object sender, LinkTappedEventArgs e) { // e.Link contains the link URL Browser.OpenAsync(e.Link); } Implementation Notes SkiaMarkdownLabel implements a lightweight Markdown parser optimized for display, not full CommonMark compliance The parser focuses on the most commonly used Markdown syntax for mobile applications For more complex Markdown rendering needs, consider creating a custom renderer Special Labels SkiaLabelFps A specialized label for displaying frames-per-second (FPS) metrics, useful for performance monitoring during development: <DrawUi:SkiaLabelFps TextColor=\"Green\" FontSize=\"12\" HorizontalOptions=\"End\" VerticalOptions=\"Start\" Margin=\"0,20,20,0\" /> Example: Text Card <DrawUi:SkiaShape Type=\"Rectangle\" BackgroundColor=\"White\" CornerRadius=\"8\" Padding=\"16\" WidthRequest=\"300\"> <DrawUi:SkiaShape.Shadows> <DrawUi:SkiaShadow Color=\"#22000000\" BlurRadius=\"10\" Offset=\"0,2\" /> </DrawUi:SkiaShape.Shadows> <DrawUi:SkiaLayout LayoutType=\"Column\" Spacing=\"8\"> <DrawUi:SkiaLabel Text=\"Article Title\" FontSize=\"20\" FontWeight=\"700\" TextColor=\"#333333\" /> <DrawUi:SkiaLabel Text=\"Published on April 3, 2025\" FontSize=\"12\" TextColor=\"#666666\" /> <DrawUi:SkiaLabel Text=\"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.\" FontSize=\"14\" TextColor=\"#444444\" LineHeight=\"1.5\" /> <DrawUi:SkiaLabel> <DrawUi:SkiaLabel.Spans> <DrawUi:TextSpan Text=\"Read more \" TextColor=\"#444444\" FontSize=\"14\" /> <DrawUi:TextSpan Text=\"here\" TextColor=\"Blue\" FontSize=\"14\" IsUnderline=\"True\" /> </DrawUi:SkiaLabel.Spans> </DrawUi:SkiaLabel> </DrawUi:SkiaLayout> </DrawUi:SkiaShape>"}, "articles/first-app.html": {"href": "articles/first-app.html", "title": "Your First DrawnUi App | DrawnUi Documentation", "summary": "Your First DrawnUi App This quickstart guide will help you create your first DrawnUi.Maui application from scratch. Prerequisites .NET 8 or later Visual Studio 2022+ (with MAUI workload) or VS Code 1. Create a New MAUI Project dotnet new maui -n MyDrawnUiApp cd MyDrawnUiApp 2. Add DrawnUi to Your Project dotnet add package DrawnUi 3. Add a DrawnUi Canvas to MainPage Open MainPage.xaml and replace the content with: <ContentPage xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\" xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\" xmlns:draw=\"clr-namespace:DrawnUi.Maui;assembly=DrawnUi.Maui\" x:Class=\"MyDrawnUiApp.MainPage\"> <draw:Canvas HorizontalOptions=\"Fill\" VerticalOptions=\"Fill\"> <draw:SkiaLayout LayoutType=\"Column\" Padding=\"32\" Spacing=\"24\"> <draw:SkiaLabel Text=\"Hello, DrawnUi!\" FontSize=\"32\" TextColor=\"Blue\" /> <draw:SkiaButton Text=\"Click Me\" Tapped=\"OnButtonTapped\" /> </draw:SkiaLayout> </draw:Canvas> </ContentPage> 4. Handle Button Tap in Code In MainPage.xaml.cs: private void OnButtonTapped(object sender, EventArgs e) { // Show a message or update UI DisplayAlert(\"DrawnUi\", \"Button tapped!\", \"OK\"); } 5. Run Your App Build and run your app on Windows, Android, iOS, or Mac. Next Steps Explore the Controls documentation Try out Samples Read about Advanced features Welcome to the DrawnUi community!"}, "articles/getting-started.html": {"href": "articles/getting-started.html", "title": "Getting Started with DrawnUi | DrawnUi Documentation", "summary": "Getting Started with DrawnUi This guide will help you get started with DrawnUi in your .NET MAUI application. Installation 1. Add the NuGet Package Install the DrawnUi NuGet package in your .NET MAUI project: dotnet add package AppoMobi.Maui.DrawnUi You might need at least the following maui versions inside your csproj: <ItemGroup> <PackageReference Include=\"Microsoft.Maui.Controls\" Version=\"9.0.30\" /> <PackageReference Include=\"Microsoft.Maui.Controls.Compatibility\" Version=\"9.0.30\" /> </ItemGroup> 2. Initialize in Your MAUI App Update your MauiProgram.cs file to initialize draw: using DrawnUi.Draw; public static class MauiProgram { public static MauiApp CreateMauiApp() { var builder = MauiApp.CreateBuilder(); builder .UseMauiApp<App>() .UseDrawnUi() // <---- Add this line .ConfigureFonts(fonts => { fonts.AddFont(\"OpenSans-Regular.ttf\", \"OpenSansRegular\"); fonts.AddFont(\"OpenSans-Semibold.ttf\", \"OpenSansSemibold\"); }); return builder.Build(); } } Add Namespace to XAML Add the DrawnUi namespace to your XAML files: <ContentPage xmlns=\"http://schemas.microsoft.com/dotnet/2021/maui\" xmlns:x=\"http://schemas.microsoft.com/winfx/2009/xaml\" xmlns:draw=\"http://schemas.appomobi.com/drawnUi/2023/draw\" x:Class=\"YourNamespace.YourPage\"> <!-- Page content --> </ContentPage> using DrawnUi Controls Now you can add DrawnUi controls to your page: <draw:DrawnUiBasePage> <draw:SkiaLayout> <draw:SkiaLabel Text=\"Hello DrawnUi!\" FontSize=\"24\" HorizontalOptions=\"Center\" VerticalOptions=\"Center\" /> <draw:SkiaButton Text=\"Click Me\" WidthRequest=\"120\" HeightRequest=\"40\" CornerRadius=\"8\" BackgroundColor=\"Blue\" TextColor=\"White\" VerticalOptions=\"Center\" HorizontalOptions=\"Center\" Margin=\"0,50,0,0\" Clicked=\"OnButtonClicked\" /> </draw:SkiaLayout> </draw:DrawnUiBasePage> Handling Events Handle control events in your code-behind: private void OnButtonClicked(object sender, SkiaGesturesParameters e) { // Handle button click } Using Platform-Specific Styles DrawnUi controls support platform-specific styling: <draw:SkiaButton Text=\"Platform Style\" ControlStyle=\"Platform\" WidthRequest=\"150\" HeightRequest=\"40\" /> <draw:SkiaSwitch ControlStyle=\"Platform\" IsToggled=\"true\" Margin=\"0,20,0,0\" /> Next Steps Explore the Controls documentation to learn about available controls See Platform-Specific Styling for more styling options Check out the Sample Applications for complete examples"}, "articles/index.html": {"href": "articles/index.html", "title": "Articles | DrawnUi Documentation", "summary": "Articles Note: this is under heavy construction and may contain some outdated or non-exact information as it was mainly auto-generated, including articles. This section contains documentation articles and guides for using DrawnUi. Getting Started Installation and Setup Your First DrawnUi App Understanding the Drawing Pipeline Controls Overview Buttons Switches and Toggles Layout Controls Text and Labels Images Advanced Topics Performance Optimization Custom Controls Animation Platform-Specific Styling"}, "articles/samples.html": {"href": "articles/samples.html", "title": "Samples | DrawnUi Documentation", "summary": "Samples Explore real-world examples and code snippets to help you get started and master DrawnUi.Maui. Example Apps Sandbox: A playground app demonstrating most controls and features. See the samples/Sandbox folder in the repository. TestCalendar: Example of a custom calendar UI built with DrawnUi.Maui. Code Snippets Game UI & Interactive Games Gradients SkiaScroll & Virtualization Gestures & Touch Input How to Run the Samples Clone the repository. Open the solution in Visual Studio or your preferred IDE. Set the desired sample project (e.g., Sandbox) as the startup project. Run and explore the code. Contributing Samples Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!"}, "index.html": {"href": "index.html", "title": "DrawnUi Documentation | DrawnUi Documentation", "summary": "DrawnUi Documentation Note: this is under heavy construction and may contain some outdated or non-exact information as it was mainly auto-generated, including articles. Rendering engine to draw your UI on a Skia canvas, with gestures and animations, designed to draw pixel-perfect custom controls instead of using native ones, powered by SkiaSharp😍. Create and render your custom controls on a hardware-accelerated Skia canvas with an improved common MAUI layout system. Supports iOS, MacCatalyst, Android, Windows. To use inside a usual MAUI app, consume drawn controls here and there inside Canvas views. Create a totally drawn app with just one Canvas as root view, SkiaShell is provided for navigation. Drawn controls are totally virtual, these are commands for the engine on what and how to draw on a skia canvas. Free to use under the MIT license, a nuget package is available. About A small article about the library and why it was created Demo Apps This repo includes a Sandbox project for some custom controls, with playground examples, custom controls, maps etc More creating custom controls examples inside the Engine Demo 🤩 Updated with latest nuget! A dynamic arcade game drawn with this engine, uses preview nuget with SkiaSharp v3. A drawn CollectionView demo where you could see how simple and profitable it is to convert an existing recycled cells list into a drawn one Shaders Carousel Demo featuring SkiaSharp v3 capabilities Features SkiaSharp Rendering: All controls are rendered using SkiaSharp for maximum performance Platform Styling: Automatic styling based on the current platform (iOS, Android, Windows) Rich Controls: Buttons, switches, checkboxes, and more with full styling support Animation: Built-in animation capabilities for rich, interactive UIs Customization: Extensive customization options for all controls Documentation Structure This documentation is organized into the following sections: Getting Started: Quick start guide and installation Controls: Detailed documentation for each control API Reference: Complete API documentation Samples: Example applications and code snippets"}}
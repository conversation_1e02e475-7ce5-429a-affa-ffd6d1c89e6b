﻿uniform float4 iMouse;            
uniform float  iTime;             
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution;  
uniform shader iImage1;  // Texture
uniform shader iImage2;   
uniform float2 iOffset;  // Top-left corner of DrawingRect
uniform float2 iOrigin;  
uniform float4 iMargins;  


 half4 texture(float2 coords, float lod){

	 vec2 adjustedCoords = coords * (1.0 + lod * 0.01);
	vec2 pixelCoord = adjustedCoords * iImageResolution.xy;
	return sample(iImage1, pixelCoord);
}

float step(float edge, float x) {
    return x < edge ? 0.0 : 1.0;
}

 
half4 main(float2 fragCoord) 
{	
    vec2 uv = fragCoord / iResolution.xy;
	
	float lod = (5.0 + 5.0*sin( iTime ))*step( uv.x, 0.5 );
	
	vec3 col = texture( vec2(uv.x,1.0-uv.y), lod ).xyz;
	
	return vec4( col, 1.0 );
}

 
﻿
// Custom parameters
float size = 0.2;

// Author: gre
// License: MIT

float rand (vec2 co) {
  return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);
}

vec4 transition (vec2 uv) {
  float r = rand(vec2(0, uv.y));
  float m = smoothstep(0.0, -size, uv.x*(1.0-size) + size*r - (progress * (1.0 + size)));
  return mix(
    getFromColor(uv),
    getToColor(uv),
    m
  );
}

half4 main(float2 fragCoord) {
    // Normalize the coordinates
    float2 normCoord = (fragCoord - iOffset) / iResolution;
    normCoord.y = 1.0 - normCoord.y;
    half4 fragColor = transition(normCoord);
    return fragColor;
}

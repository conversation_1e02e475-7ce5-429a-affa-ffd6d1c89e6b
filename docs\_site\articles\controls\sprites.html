<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Sprite Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Sprite Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="sprite-controls">Sprite Controls</h1>

<p>DrawnUi provides specialized controls for rendering sprite-based animations. This article covers the sprite animation components available in the framework.</p>
<h2 id="skiasprite">SkiaSprite</h2>
<p>SkiaSprite is a high-performance control for displaying and animating sprite sheets. It loads sprite sheets (a single image containing multiple animation frames arranged in a grid) and renders individual frames with precise timing for smooth animations.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaSprite
    Source=&quot;sprites/explosion.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;4&quot;
    FramesPerSecond=&quot;24&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;-1&quot;
    WidthRequest=&quot;128&quot;
    HeightRequest=&quot;128&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Source</code></td>
<td>string</td>
<td>Path or URL of the sprite sheet image</td>
</tr>
<tr>
<td><code>Columns</code></td>
<td>int</td>
<td>Number of columns in the sprite sheet grid</td>
</tr>
<tr>
<td><code>Rows</code></td>
<td>int</td>
<td>Number of rows in the sprite sheet grid</td>
</tr>
<tr>
<td><code>FramesPerSecond</code></td>
<td>int</td>
<td>Animation speed in frames per second (default: 24)</td>
</tr>
<tr>
<td><code>MaxFrames</code></td>
<td>int</td>
<td>Maximum number of frames to use (0 means use all)</td>
</tr>
<tr>
<td><code>CurrentFrame</code></td>
<td>int</td>
<td>Current frame being displayed (0-based index)</td>
</tr>
<tr>
<td><code>FrameSequence</code></td>
<td>int[]</td>
<td>Custom sequence of frames to play</td>
</tr>
<tr>
<td><code>AnimationName</code></td>
<td>string</td>
<td>Name of a predefined animation sequence</td>
</tr>
<tr>
<td><code>AutoPlay</code></td>
<td>bool</td>
<td>Whether animation starts automatically when loaded</td>
</tr>
<tr>
<td><code>Repeat</code></td>
<td>int</td>
<td>Number of times to repeat (-1 for infinite)</td>
</tr>
<tr>
<td><code>SpeedRatio</code></td>
<td>double</td>
<td>Adjusts animation speed (1.0 is normal speed)</td>
</tr>
<tr>
<td><code>DefaultFrame</code></td>
<td>int</td>
<td>Frame to display when not playing</td>
</tr>
</tbody>
</table>
<h3 id="animation-control">Animation Control</h3>
<p>Control playback programmatically:</p>
<pre><code class="lang-csharp">// Start animation
mySprite.Start();

// Stop animation
mySprite.Stop();

// Jump to a specific frame
mySprite.CurrentFrame = 5;

// Seek to a time position
mySprite.Seek(timeInMs);
</code></pre>
<h3 id="animation-events">Animation Events</h3>
<pre><code class="lang-csharp">// Animation started event
mySprite.Started += (sender, e) =&gt; {
    // Animation has started
};

// Animation completed event (fires after all repeats)
mySprite.Finished += (sender, e) =&gt; {
    // Animation has finished
};
</code></pre>
<h3 id="sprite-sheet-structure">Sprite Sheet Structure</h3>
<p>A sprite sheet is a single image containing multiple frames arranged in a grid:</p>
<pre><code>+---+---+---+---+
| 0 | 1 | 2 | 3 |
+---+---+---+---+
| 4 | 5 | 6 | 7 |
+---+---+---+---+
| 8 | 9 | 10| 11|
+---+---+---+---+
</code></pre>
<p>The <code>Columns</code> and <code>Rows</code> properties define the grid structure:</p>
<ul>
<li>In the example above, set <code>Columns=&quot;4&quot;</code> and <code>Rows=&quot;3&quot;</code></li>
<li>Frames are numbered left-to-right, top-to-bottom (0 to 11)</li>
<li>Each frame must have the same dimensions</li>
</ul>
<h3 id="loading-sprite-sheets">Loading Sprite Sheets</h3>
<p>SkiaSprite supports loading sprite sheets from various sources:</p>
<pre><code class="lang-xml">&lt;!-- From app resources --&gt;
&lt;draw:SkiaSprite Source=&quot;running_character.png&quot; /&gt;

&lt;!-- From local file --&gt;
&lt;draw:SkiaSprite Source=&quot;file:///path/to/animation.png&quot; /&gt;

&lt;!-- From URL --&gt;
&lt;draw:SkiaSprite Source=&quot;https://example.com/sprites/animation.png&quot; /&gt;
</code></pre>
<h3 id="creating-a-character-animation">Creating a Character Animation</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;200&quot;
    BackgroundColor=&quot;#F0F0F0&quot;&gt;
    
    &lt;draw:SkiaSprite
        x:Name=&quot;PlayerAnimation&quot;
        Source=&quot;character_run.png&quot;
        Columns=&quot;8&quot;
        Rows=&quot;1&quot;
        FramesPerSecond=&quot;12&quot;
        AutoPlay=&quot;False&quot;
        WidthRequest=&quot;128&quot;
        HeightRequest=&quot;128&quot;
        HorizontalOptions=&quot;Center&quot;
        VerticalOptions=&quot;Center&quot; /&gt;
        
    &lt;draw:SkiaButton
        Text=&quot;Run&quot;
        WidthRequest=&quot;80&quot;
        HeightRequest=&quot;40&quot;
        Margin=&quot;0,140,0,0&quot;
        HorizontalOptions=&quot;Center&quot;
        Tapped=&quot;OnRunButtonTapped&quot; /&gt;
        
&lt;/draw:SkiaLayout&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private void OnRunButtonTapped(object sender, EventArgs e)
{
    if (PlayerAnimation.IsPlaying)
    {
        PlayerAnimation.Stop();
    }
    else
    {
        PlayerAnimation.Start();
    }
}
</code></pre>
<h3 id="animation-states">Animation States</h3>
<p>Use the <code>DefaultFrame</code> property to control which frame is shown when the animation is not playing:</p>
<pre><code class="lang-xml">&lt;!-- Show first frame when not playing --&gt;
&lt;draw:SkiaSprite
    Source=&quot;button_press.png&quot;
    Columns=&quot;10&quot;
    Rows=&quot;1&quot;
    DefaultFrame=&quot;0&quot; /&gt;

&lt;!-- Show last frame when not playing (useful for transitions that should remain in end state) --&gt;
&lt;draw:SkiaSprite
    Source=&quot;door_open.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;1&quot;
    DefaultFrame=&quot;7&quot; /&gt;
</code></pre>
<h3 id="advanced-frame-sequences-and-reusing-spritesheets">Advanced: Frame Sequences and Reusing Spritesheets</h3>
<p>One of the key features of SkiaSprite is the ability to create multiple animations from a single spritesheet by defining frame sequences:</p>
<h4 id="using-frame-sequences-directly">Using Frame Sequences Directly</h4>
<pre><code class="lang-xml">&lt;!-- Manual frame sequence in XAML using array converter --&gt;
&lt;draw:SkiaSprite
    Source=&quot;character.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;2&quot;
    FrameSequence=&quot;{Binding FrameSequence, Converter={StaticResource IntArrayConverter}}&quot;
    FramesPerSecond=&quot;12&quot; /&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">// Define a specific frame sequence
mySprite.FrameSequence = new[] { 3, 4, 5, 4, 3 }; // Play frames in this exact order
</code></pre>
<h4 id="creating-reusable-named-animations">Creating Reusable Named Animations</h4>
<p>Register animations once at application startup:</p>
<pre><code class="lang-csharp">// In your App.xaml.cs or similar initialization code
protected override void OnStart()
{
    base.OnStart();
    
    // Register named animations for a character spritesheet
    SkiaSprite.CreateAnimationSequence(&quot;Idle&quot;, new[] { 0, 1, 2, 1 });
    SkiaSprite.CreateAnimationSequence(&quot;Walk&quot;, new[] { 3, 4, 5, 6, 7, 8 });
    SkiaSprite.CreateAnimationSequence(&quot;Jump&quot;, new[] { 9, 10, 11 });
    SkiaSprite.CreateAnimationSequence(&quot;Attack&quot;, new[] { 12, 13, 14, 15 });
}
</code></pre>
<p>Then in XAML just reference by name:</p>
<pre><code class="lang-xml">&lt;!-- Multiple sprites sharing the same spritesheet with different animations --&gt;
&lt;draw:SkiaSprite Source=&quot;character.png&quot; AnimationName=&quot;Walk&quot; /&gt;
&lt;draw:SkiaSprite Source=&quot;character.png&quot; AnimationName=&quot;Attack&quot; /&gt;
</code></pre>
<p>Or switch animations in code:</p>
<pre><code class="lang-csharp">// Change the animation based on character state
void UpdateCharacterState(PlayerState state)
{
    switch (state)
    {
        case PlayerState.Idle:
            characterSprite.AnimationName = &quot;Idle&quot;;
            break;
        case PlayerState.Walking:
            characterSprite.AnimationName = &quot;Walk&quot;;
            break;
        case PlayerState.Jumping:
            characterSprite.AnimationName = &quot;Jump&quot;;
            break;
        case PlayerState.Attacking:
            characterSprite.AnimationName = &quot;Attack&quot;;
            break;
    }
}
</code></pre>
<h3 id="memory-management-and-caching">Memory Management and Caching</h3>
<p>SkiaSprite includes an intelligent caching system to avoid reloading the same spritesheets multiple times:</p>
<pre><code class="lang-csharp">// Clear the entire spritesheet cache
SkiaSprite.ClearCache();

// Remove a specific spritesheet from cache
SkiaSprite.RemoveFromCache(&quot;character.png&quot;);
</code></pre>
<p>The control automatically handles:</p>
<ul>
<li>Caching spritesheets in memory when first loaded</li>
<li>Sharing the same bitmap instance between multiple SkiaSprite controls</li>
<li>Safe disposal when controls are no longer used</li>
</ul>
<h3 id="advanced-custom-animation-speed">Advanced: Custom Animation Speed</h3>
<p>Adjust animation speed using <code>SpeedRatio</code>:</p>
<pre><code class="lang-xml">&lt;!-- Half speed --&gt;
&lt;draw:SkiaSprite
    Source=&quot;walking.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;1&quot;
    SpeedRatio=&quot;0.5&quot; /&gt;

&lt;!-- Double speed --&gt;
&lt;draw:SkiaSprite
    Source=&quot;running.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;1&quot;
    SpeedRatio=&quot;2.0&quot; /&gt;
</code></pre>
<h3 id="example-button-with-animated-states">Example: Button with Animated States</h3>
<pre><code class="lang-xml">&lt;draw:SkiaShape
    Type=&quot;Rectangle&quot;
    CornerRadius=&quot;8&quot;
    BackgroundColor=&quot;#3498DB&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;60&quot;&gt;
    
    &lt;draw:SkiaHotspot Tapped=&quot;OnButtonTapped&quot;&gt;
        &lt;draw:SkiaLayout
            HorizontalOptions=&quot;Fill&quot;
            VerticalOptions=&quot;Fill&quot;&gt;
            
            &lt;!-- Button text --&gt;
            &lt;draw:SkiaLabel
                Text=&quot;Click Me&quot;
                TextColor=&quot;White&quot;
                FontSize=&quot;18&quot;
                HorizontalOptions=&quot;Center&quot;
                VerticalOptions=&quot;Center&quot; /&gt;
                
            &lt;!-- Button animation that plays on tap --&gt;
            &lt;draw:SkiaSprite
                x:Name=&quot;ButtonAnimation&quot;
                Source=&quot;button_press.png&quot;
                Columns=&quot;5&quot;
                Rows=&quot;1&quot;
                FramesPerSecond=&quot;30&quot;
                AutoPlay=&quot;False&quot;
                Repeat=&quot;0&quot;
                HorizontalOptions=&quot;Fill&quot;
                VerticalOptions=&quot;Fill&quot;
                Opacity=&quot;0.5&quot; /&gt;
                
        &lt;/draw:SkiaLayout&gt;
    &lt;/draw:SkiaHotspot&gt;
    
&lt;/draw:SkiaShape&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private void OnButtonTapped(object sender, EventArgs e)
{
    ButtonAnimation.Stop();
    ButtonAnimation.CurrentFrame = 0;
    ButtonAnimation.Start();
}
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="memory-management">Memory Management</h3>
<ul>
<li>Sprite sheets are cached automatically to avoid redundant loading</li>
<li>For large or numerous sprite sheets, consider monitoring memory usage</li>
<li>Use <code>ClearCache()</code> or <code>RemoveFromCache()</code> when spritesheets are no longer needed</li>
</ul>
<h3 id="optimization-tips">Optimization Tips</h3>
<ol>
<li><p><strong>Sprite Sheet Size</strong></p>
<ul>
<li>Keep sprite sheets as small as possible while maintaining required quality</li>
<li>Consider using sprite packing algorithms to maximize space efficiency</li>
<li>Use power-of-two dimensions for better GPU compatibility</li>
</ul>
</li>
<li><p><strong>Frame Rate</strong></p>
<ul>
<li>Choose an appropriate <code>FramesPerSecond</code> value for your animation</li>
<li>For simple character animations, 12-15 FPS is often sufficient</li>
<li>For smoother animations, 24-30 FPS provides better results</li>
<li>Higher frame rates consume more resources</li>
</ul>
</li>
<li><p><strong>Frame Sequences</strong></p>
<ul>
<li>For complex animations, use frame sequences to avoid redundant frames</li>
<li>Share spritesheets between multiple sprites using the built-in caching</li>
</ul>
</li>
<li><p><strong>Image Format</strong></p>
<ul>
<li>Use PNG for sprite sheets with transparency</li>
<li>Consider WebP for better compression if supported</li>
<li>Optimize image file size using appropriate compression tools</li>
</ul>
</li>
</ol>
<h3 id="implementation-notes">Implementation Notes</h3>
<p>The <code>SkiaSprite</code> control derives from <code>AnimatedFramesRenderer</code>, which provides the base functionality for frame-based animation. The control internally:</p>
<ol>
<li>Loads a spritesheet image into an <code>SKBitmap</code></li>
<li>Calculates frame dimensions based on <code>Columns</code> and <code>Rows</code></li>
<li>Extracts individual frames on demand by creating a new bitmap for each frame</li>
<li>Uses a <code>SkiaImage</code> control to display the current frame</li>
<li>Manages animation timing through the inherited animator functionality</li>
</ol>
<p>This architecture aligns with other animation controls in DrawnUi like <code>SkiaGif</code> and <code>SkiaLottie</code>.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi.Maui/blob/master/docs/articles/controls/sprites.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

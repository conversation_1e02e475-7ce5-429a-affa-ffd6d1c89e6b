# Demo

This section is dedicated to showcasing demo applications and interactive examples built with DrawnUI for .NET MAUI.

- Explore real-world usage of DrawnUI controls and layouts.
- See how to implement custom controls and advanced UI scenarios.
- Find inspiration for your own projects.

* DrawnUI repository contains a Sandbox project with some custom controls, playground examples, maps etc

* The main demo app is the [Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo) 🤩 

* [Shaders Carousel Demo](https://github.com/taublast/ShadersCarousel/) featuring SkiaSharp v3 capabilities

* A [drawn CollectionView demo](https://github.com/taublast/SurfAppCompareDrawn) where you could see how simple and profitable it is to convert an existing recycled cells list into a drawn one

* A [dynamic arcade game](https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter) drawn with this engine, uses preview nuget with SkiaSharp v3.

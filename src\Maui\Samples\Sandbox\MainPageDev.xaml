﻿<?xml version="1.0" encoding="utf-8"?>

<views:BasePageCodeBehind
    x:Class="MauiNet8.MainPageDev"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mauiNet8="clr-namespace:MauiNet8"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:Name="ThisPage"
    x:DataType="mauiNet8:MainPageDev">

    <VerticalStackLayout>
        <draw:Canvas Gestures="Enabled">
            <draw:SkiaLayer VerticalOptions="Fill">

                <draw:SkiaGrid HorizontalOptions="Fill">

                    <!--#region  Grid over Shape-->
                    <draw:SkiaShape BackgroundColor="LightBlue" HorizontalOptions="Center" VerticalOptions="Center" Grid.Row="0" Grid.Column="1"
                                    HeightRequest="150" WidthRequest="150">

                        <draw:SkiaButton Tapped="SkiaButton_Tapped" 
                                         BackgroundColor="Black"
                                         HeightRequest="50" WidthRequest="50" HorizontalOptions="Center" VerticalOptions="Center">
                        </draw:SkiaButton>

                    </draw:SkiaShape>

                    <draw:SkiaLayout x:Name="TopLayer1" Type="Grid" Grid.Row="0" Grid.Column="1"  
                                     HorizontalOptions="Center" VerticalOptions="Center"  
                                     HeightRequest="150" LockRatio="1"
                                     BlockGesturesBelow="True">
                        <draw:SkiaShape BackgroundColor="#6600FF00" HeightRequest="150" WidthRequest="150" >
                            <draw:SkiaLabel Text="Press Here! I'm in a Grid" Margin="10" TextColor="Black" FontSize="20" HorizontalOptions="Center" VerticalOptions="Center"/>
                        </draw:SkiaShape>
                    </draw:SkiaLayout>

                    <!--#endregion-->

                    <!--#region Shape over Shape-->
                    <draw:SkiaShape BackgroundColor="LightBlue" HorizontalOptions="Center" VerticalOptions="Center" Grid.Row="1" Grid.Column="1" HeightRequest="150" WidthRequest="150">
                        <draw:SkiaButton Tapped="SkiaButton_Tapped" 
                    BackgroundColor="Red" HeightRequest="50" WidthRequest="50" HorizontalOptions="Center" VerticalOptions="Center">
                        </draw:SkiaButton>
                    </draw:SkiaShape>


                    <draw:SkiaShape
                        x:Name="TopLayer2" BackgroundColor="#6600FF00" 
                        HeightRequest="150" LockRatio="1"
                        Grid.Row="1" Grid.Column="1" 
                        BlockGesturesBelow="True"  
                        HorizontalOptions="Center" VerticalOptions="Center">

                        <draw:SkiaLabel Text="Press Here! I'm in a shape" Margin="10" TextColor="Black" FontSize="20" HorizontalOptions="Center" VerticalOptions="Center"/>
                    </draw:SkiaShape>
                    <!--#endregion-->


                </draw:SkiaGrid>

            </draw:SkiaLayer>
        </draw:Canvas>
    </VerticalStackLayout>

</views:BasePageCodeBehind>
﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainPageMapsui"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mapsUi="clr-namespace:DrawnUi.MapsUi;assembly=DrawnUi.Maui.MapsUi"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:Name="ThisPage"
    x:DataType="demo:MainPageViewModel">

    <draw:Canvas
        BackgroundColor="Gainsboro"
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        Tag="MainPage">

        <draw:Canvas.Resources>
            <ResourceDictionary>

                <Style x:Key="SkiaStyleControlInTable" TargetType="draw:SkiaControl">
                    <Setter Property="VerticalOptions" Value="Center" />
                    <Setter Property="AddMarginLeft" Value="120" />
                </Style>

                <Style x:Key="SkiaStyleControLabel" TargetType="draw:SkiaLabel">
                    <Setter Property="VerticalOptions" Value="Center" />
                    <Setter Property="TextColor" Value="Black" />
                </Style>

                <Style x:Key="SkiaStyleControlContainer" TargetType="draw:SkiaLayout">
                    <Setter Property="HeightRequest" Value="44" />
                    <Setter Property="HorizontalOptions" Value="Fill" />
                    <Setter Property="UseCache" Value="Image" />
                </Style>

            </ResourceDictionary>
        </draw:Canvas.Resources>

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tag="WithMap"
            VerticalOptions="Fill">

            <!--  MAP  -->

            <!--  GPU cache makes a huge difference  -->
            <mapsUi:SkiaMapsUi
                x:Name="mapControl"
                AddMarginBottom="{Binding OffsetMap}"
                DisableEffects="{Binding ValueSwitch, Converter={x:StaticResource NotConverter}}"
                HorizontalOptions="Fill"
                UseCache="GPU"
                VerticalOptions="Fill"
                ZIndex="-1">

                <draw:SkiaControl.VisualEffects>

                    <draw:TintWithAlphaEffect
                        x:DataType="demo:MainPageViewModel"
                        Alpha="{Binding Value3}"
                        ColorTint="{Binding SelectedColor}" />

                    <draw:ChainAdjustContrastEffect x:DataType="demo:MainPageViewModel" Value="{Binding Value1}" />

                    <draw:ChainAdjustLightnessEffect x:DataType="demo:MainPageViewModel" Value="{Binding Value2}" />

                    <draw:ChainSaturationEffect x:DataType="demo:MainPageViewModel" Value="{Binding Value6}" />


                </draw:SkiaControl.VisualEffects>

            </mapsUi:SkiaMapsUi>

            <!--  ATTRIBUTION  -->
            <!--
                in fullscreen can add
                AddMarginTop="{x:Static draw:Super.StatusBarHeight}"
            -->
            <draw:SkiaShape
                Margin="2"
                Padding="4,2"
                BackgroundColor="#33ffffff"
                CornerRadius="6"
                UseCache="Image">
                <draw:SkiaLabel
                    FontSize="10"
                    Text="Mapsui and OpenStreetMap"
                    TextColor="Black" />
            </draw:SkiaShape>

            <!--  DRAWER  -->
            <draw:SkiaDrawer
                BlockGesturesBelow="True"
                x:Name="Drawer"
                Direction="FromBottom"
                HeaderSize="{Binding DrawerHeaderSize}"
                HeightRequest="500"
                HorizontalOptions="Fill"
                IsOpen="{Binding IsOpen}"
                Tag="Drawer"
                UseCache="ImageComposite"
                VerticalOptions="End"
                ZIndex="1">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    Tag="ScrollInside"
                    VerticalOptions="Fill">

                    <!--  HEADER  -->
                    <draw:SkiaShape
                        BackgroundColor="White"
                        CornerRadius="20,20,0,0"
                        HeightRequest="{Binding DrawerHeaderSize}"
                        HorizontalOptions="Fill"
                        UseCache="Image">

                        <draw:SkiaShape
                            BackgroundColor="{StaticResource Gray300}"
                            CornerRadius="20"
                            HeightRequest="4"
                            HorizontalOptions="Center"
                            StrokeColor="{StaticResource Gray400}"
                            StrokeWidth="1"
                            Type="Rectangle"
                            VerticalOptions="Center"
                            WidthRequest="64" />

                    </draw:SkiaShape>

                    <!--  CONTENT  -->
                    <draw:SkiaLayout
                        Padding="24,16"
                        AddMarginTop="{Binding DrawerHeaderSize}"
                        BackgroundColor="White"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Tag="InsideDrawer"
                        Type="Column"
                        UseCache="ImageComposite"
                        VerticalOptions="Fill">

                        <!--  Switch  -->
                        <draw:SkiaLayout Style="{x:StaticResource SkiaStyleControlContainer}">
                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="Apply Effects" />
                            <controls:DrawnSwitch
                                IsToggled="{Binding ValueSwitch, Mode=TwoWay}"
                                Style="{x:StaticResource SkiaStyleControlInTable}"
                                TranslationX="-4" />
                        </draw:SkiaLayout>

                        <!--  Brightness  -->
                        <draw:SkiaLayout Style="{x:StaticResource SkiaStyleControlContainer}">
                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="{Binding Value2, StringFormat='Brightness: {0:0.00}'}" />
                            <controls:DrawnSlider
                                End="{Binding Value2, Mode=TwoWay}"
                                HorizontalOptions="Fill"
                                Max="2"
                                Min="0"
                                MinMaxStringFormat="P0"
                                Step="0.01"
                                Style="{x:StaticResource SkiaStyleControlInTable}" />
                        </draw:SkiaLayout>

                        <!--  Contrast  -->
                        <draw:SkiaLayout Style="{x:StaticResource SkiaStyleControlContainer}">
                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="{Binding Value1, StringFormat='Contrast: {0:0.00}'}" />
                            <controls:DrawnSlider
                                End="{Binding Value1, Mode=TwoWay}"
                                HorizontalOptions="Fill"
                                Max="3"
                                Min="0"
                                MinMaxStringFormat="P0"
                                Step="0.01"
                                Style="{x:StaticResource SkiaStyleControlInTable}" />
                        </draw:SkiaLayout>

                        <!--  Saturation  -->
                        <draw:SkiaLayout Style="{x:StaticResource SkiaStyleControlContainer}">
                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="{Binding Value6, StringFormat='Saturation: {0:0.00}'}" />
                            <controls:DrawnSlider
                                End="{Binding Value6, Mode=TwoWay}"
                                HorizontalOptions="Fill"
                                Max="2"
                                Min="0"
                                MinMaxStringFormat="P0"
                                Step="0.01"
                                Style="{x:StaticResource SkiaStyleControlInTable}" />
                        </draw:SkiaLayout>

                        <!--  Tint  -->
                        <draw:SkiaLayout Style="{x:StaticResource SkiaStyleControlContainer}">
                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="{Binding Value3, StringFormat='Tint: {0:0.00}'}" />
                            <controls:DrawnSlider
                                End="{Binding Value3, Mode=TwoWay}"
                                HorizontalOptions="Fill"
                                Max="1"
                                Min="0"
                                MinMaxStringFormat="P0"
                                Step="0.01"
                                Style="{x:StaticResource SkiaStyleControlInTable}" />
                        </draw:SkiaLayout>


                        <!--  Tint  COLOR Picker  -->
                        <draw:SkiaLayout
                            AddMarginBottom="{Binding BottomInsets}"
                            AddMarginTop="16"
                            HeightRequest="100"
                            Style="{x:StaticResource SkiaStyleControlContainer}">

                            <draw:SkiaLabel
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource SkiaStyleControLabel}"
                                Text="Tint Color" />

                            <controls:ColorPicker
                                DefaultColor="CornflowerBlue"
                                HorizontalOptions="Fill"
                                SelectedColor="{Binding SelectedColor}"
                                Style="{x:StaticResource SkiaStyleControlInTable}"
                                UseCache="Image"
                                WidthRequest="-1" />

                        </draw:SkiaLayout>


                    </draw:SkiaLayout>



                </draw:SkiaLayout>

            </draw:SkiaDrawer>

            <controls:ButtonToRoot />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>

﻿namespace DrawnUi.Draw;

public enum FontWeight
{
	/// <summary>
	/// The thin
	/// </summary>
	Thin = 100,

	/// <summary>
	/// Also known as Ultra Light
	/// </summary>
	ExtraLight = 200,

	/// <summary>
	/// The light
	/// </summary>
	Light = 300,

	/// <summary>
	/// Also known as Normal
	/// </summary>
	Regular = 400,

	/// <summary>
	/// The medium
	/// </summary>
	Medium = 500,

	/// <summary>
	/// The semi bold
	/// </summary>
	SemiBold = 600,

	/// <summary>
	/// The bold
	/// </summary>
	Bold = 700,

	/// <summary>
	/// Also known as Heavy or UltraBold
	/// </summary>
	ExtraBold = 800,

	/// <summary>
	/// The black
	/// </summary>
	Black = 900
}
﻿
vec3 color = vec3(0.0);
float colorPhase = 0.4 ; // if 0.0, there is no black phase, if 0.9, the black phase is very important

// author: gre
// License: MIT

vec4 transition (vec2 uv) {
  return mix(
    mix(vec4(color, 1.0), getFromColor(uv), smoothstep(1.0-colorPhase, 0.0, progress)),
    mix(vec4(color, 1.0), getToColor(uv), smoothstep(    colorPhase, 1.0, progress)),
    progress);
}

half4 main(float2 fragCoord) {
    // Normalize the coordinates
    float2 normCoord = (fragCoord - iOffset) / iResolution;
    normCoord.y = 1.0 - normCoord.y;
    half4 fragColor = transition(normCoord);
    return fragColor;
}


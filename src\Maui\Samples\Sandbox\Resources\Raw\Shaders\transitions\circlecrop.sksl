﻿vec4 bgcolor = vec4(0.0, 0.0, 0.0, 1.0);


// License: MIT
// Author: fkuteken
// ported by gre from https://gist.github.com/fkuteken/f63e3009c1143950dee9063c3b83fb88

vec4 transition(vec2 p, float s, vec2 ratio2) {
  float dist = length((p - 0.5) * ratio2);
  return mix(
    progress < 0.5 ? getFromColor(p) : getToColor(p), // branching is ok here as we statically depend on progress uniform (branching won't change over pixels)
    bgcolor,
    step(s, dist)
  );
}

half4 main(float2 fragCoord) {
    // Normalize the coordinates
    float2 normCoord = (fragCoord - iOffset) / iResolution;
    normCoord.y = 1.0 - normCoord.y;
    
    vec2 ratio2 = vec2(1.0, 1.0 / ratio);
    float s = pow(2.0 * abs(progress - 0.5), 3.0);
    half4 fragColor = transition(normCoord, s, ratio2);
    return fragColor;
}
<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>DrawnUi Documentation | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="DrawnUi Documentation | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="images/favicon.ico">
      <link rel="stylesheet" href="styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="styles/docfx.css">
      <link rel="stylesheet" href="styles/main.css">
      <meta property="docfx:navrel" content="toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="index.html">
                <img id="logo" class="svg" src="images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="drawnui-documentation">DrawnUi Documentation</h1>

<p>This folder contains the DocFX-based documentation for DrawnUi.</p>
<h2 id="building-the-documentation">Building the Documentation</h2>
<h3 id="option-1-using-net-tool-recommended">Option 1: Using .NET Tool (Recommended)</h3>
<p>To build the documentation locally:</p>
<ol>
<li><p>Install DocFX as a .NET global tool:</p>
<pre><code>dotnet tool install -g docfx
</code></pre>
</li>
<li><p>Navigate to the docs folder:</p>
<pre><code>cd docs
</code></pre>
</li>
<li><p>Build the documentation:</p>
<pre><code>docfx build
</code></pre>
</li>
<li><p>Preview the documentation:</p>
<pre><code>docfx serve _site
</code></pre>
</li>
</ol>
<h3 id="option-2-using-docker">Option 2: Using Docker</h3>
<p>If you don't have .NET installed, you can use Docker:</p>
<pre><code class="lang-bash"># From the repository root
docker run --rm -it -v ${PWD}:/app -w /app/docs mcr.microsoft.com/dotnet/sdk:7.0 bash -c &quot;dotnet tool install -g docfx &amp;&amp; docfx build&quot;
</code></pre>
<h3 id="option-3-using-npm-package-alternative">Option 3: Using NPM Package (Alternative)</h3>
<p>For environments where .NET isn't available:</p>
<ol>
<li><p>Install docfx via npm:</p>
<pre><code>npm install -g @tsgkadot/docfx-flavored-markdown
</code></pre>
</li>
<li><p>Build the documentation:</p>
<pre><code>dfm build
</code></pre>
</li>
</ol>
<h2 id="documentation-structure">Documentation Structure</h2>
<ul>
<li><code>/api/</code>: Auto-generated API documentation from XML comments</li>
<li><code>/articles/</code>: Conceptual documentation articles and tutorials</li>
<li><code>/images/</code>: Images used in the documentation</li>
<li><code>/templates/</code>: DocFX templates for styling</li>
</ul>
<h2 id="contributing-to-the-documentation">Contributing to the Documentation</h2>
<p>When contributing to the documentation:</p>
<ol>
<li>For API documentation, add XML comments to the code in the DrawnUi source files</li>
<li>For conceptual documentation, edit or create Markdown files in the <code>/articles/</code> folder</li>
<li>After making changes, build the documentation to verify it renders correctly</li>
</ol>
<h2 id="api-documentation-guidelines">API Documentation Guidelines</h2>
<p>When adding XML comments to your code:</p>
<ul>
<li>Use the <code>&lt;summary&gt;</code> tag to provide a brief description of the class/method/property</li>
<li>Use the <code>&lt;param&gt;</code> tag to document parameters</li>
<li>Use the <code>&lt;returns&gt;</code> tag to document return values</li>
<li>Use the <code>&lt;example&gt;</code> tag to provide usage examples</li>
<li>Use <code>&lt;see cref=&quot;...&quot;/&gt;</code> to create links to other types/members</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi.Maui/blob/master/docs/README.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="styles/docfx.js"></script>
    <script type="text/javascript" src="styles/main.js"></script>
  </body>
</html>

<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Platform-Specific Styling | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Platform-Specific Styling | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="platform-specific-styling">Platform-Specific Styling</h1>

<p>DrawnUi controls support platform-specific styling to ensure your app looks and feels native on each platform.</p>
<h2 id="using-platform-styles">Using Platform Styles</h2>
<h3 id="the-controlstyle-property">The ControlStyle Property</h3>
<p>Many DrawnUi controls include a <code>ControlStyle</code> property that determines their visual appearance:</p>
<ul>
<li><code>Unset</code>: Default styling defined by the control</li>
<li><code>Platform</code>: Automatically selects the appropriate style for the current platform</li>
<li><code>Cupertino</code>: iOS-style appearance</li>
<li><code>Material</code>: Android Material Design appearance</li>
<li><code>Windows</code>: Windows-style appearance</li>
</ul>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;!-- Automatically use the platform-specific style --&gt;
&lt;draw:SkiaButton
    Text=&quot;Platform Button&quot;
    ControlStyle=&quot;Platform&quot; /&gt;

&lt;!-- Explicitly use iOS style on any platform --&gt;
&lt;draw:SkiaSwitch
    ControlStyle=&quot;Cupertino&quot;
    IsToggled=&quot;true&quot; /&gt;
</code></pre>
<h2 id="supported-controls">Supported Controls</h2>
<p>The following controls support platform-specific styling:</p>
<ul>
<li><code>SkiaButton</code>: Different button appearances across platforms</li>
<li><code>SkiaSwitch</code>: Toggle switches with platform-specific track and thumb styling</li>
<li><code>SkiaCheckbox</code>: Checkbox controls with platform-appropriate checkmarks and animations</li>
</ul>
<h2 id="platform-style-characteristics">Platform Style Characteristics</h2>
<h3 id="cupertino-ios-style">Cupertino (iOS) Style</h3>
<ul>
<li>Rounded corners and subtle shadows</li>
<li>Blue accent color (#007AFF)</li>
<li>Switches have pill-shaped tracks with shadows on the thumb</li>
<li>Buttons typically have semibold text</li>
</ul>
<h3 id="material-android-style">Material (Android) Style</h3>
<ul>
<li>Less rounded corners</li>
<li>More pronounced shadows</li>
<li>Material blue accent color (#2196F3)</li>
<li>Switches have track colors that match the thumb when active</li>
<li>Buttons often use uppercase text</li>
</ul>
<h3 id="windows-style">Windows Style</h3>
<ul>
<li>Minimal corner radius</li>
<li>Subtle shadows</li>
<li>Windows blue accent color (#0078D7)</li>
<li>Switches and buttons have a more squared appearance</li>
</ul>
<h2 id="customizing-platform-styles">Customizing Platform Styles</h2>
<p>You can combine platform styles with custom styling. The platform style defines the base appearance, while your custom properties provide additional customization:</p>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Custom Platform Button&quot;
    ControlStyle=&quot;Platform&quot;
    BackgroundColor=&quot;Purple&quot;
    TextColor=&quot;White&quot; /&gt;
</code></pre>
<p>This creates a button with the platform-specific shape, shadow, and behavior, but with your custom colors.</p>
<h2 id="creating-custom-platform-styled-controls">Creating Custom Platform-Styled Controls</h2>
<p>If you're creating custom controls, you can leverage the same platform styling system:</p>
<pre><code class="lang-csharp">public class MyCustomControl : SkiaControl
{
    public static readonly BindableProperty ControlStyleProperty = BindableProperty.Create(
        nameof(ControlStyle),
        typeof(PrebuiltControlStyle),
        typeof(MyCustomControl),
        PrebuiltControlStyle.Unset);

    public PrebuiltControlStyle ControlStyle
    {
        get { return (PrebuiltControlStyle)GetValue(ControlStyleProperty); }
        set { SetValue(ControlStyleProperty, value); }
    }
    
    protected override void OnPropertyChanged(string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);
        
        if (propertyName == nameof(ControlStyle))
        {
            ApplyPlatformStyle();
        }
    }
    
    private void ApplyPlatformStyle()
    {
        switch (ControlStyle)
        {
            case PrebuiltControlStyle.Cupertino:
                // Apply iOS-specific styling
                break;
            case PrebuiltControlStyle.Material:
                // Apply Material Design styling
                break;
            case PrebuiltControlStyle.Windows:
                // Apply Windows styling
                break;
            case PrebuiltControlStyle.Platform:
                #if IOS || MACCATALYST
                // Apply iOS styling
                #elif ANDROID
                // Apply Material styling
                #elif WINDOWS
                // Apply Windows styling
                #endif
                break;
        }
    }
}
</code></pre>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi.Maui/blob/master/docs/articles/advanced/platform-styling.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;  // Texture
uniform shader iImage2;  // Texture Mask
uniform float2 iOffset;  // Top-left corner of DrawingRect
uniform float2 iOrigin;  
uniform float4 iMargins;  

uniform float2 direction;

//todo pass as parameters
const float maxSigma = 10.0;
const float k = 3.0;
const float windowSize = k * maxSigma;
const float halfWindowSize = windowSize / 2.0;
 
const float  PI = 3.14159265359;
const vec4 backgroundColor = vec4(0.0, 0.0, 0.0, 0.0);


// Function to calculate Gaussian weight
float Gaussian(float x, float sigma) {
  return exp(-(x * x) / (2.0 * sigma * sigma)) / (2.0 * 3.14159 * sigma * sigma);
}

// Function to perform blur in one direction
vec3 blur(vec2 uv, vec2 direction, float sigma) 
{
  vec3 result = vec3(0.0);
  float totalWeight = 0.0;
  float window = sigma * k * 0.5;

  for (float i = -halfWindowSize; i <= halfWindowSize; i++) {
      if (abs(i) > window) {
        continue;
      }
      float weight = Gaussian(i, sigma);
      vec2 offset = vec2(direction * i);
      //vec3 sample = iImage1.eval(uv + offset).rgb;
      vec3 sample = sample(iImage1, uv + offset).rgb;

      result += sample * weight;
      totalWeight += weight;
  }

  if (totalWeight > 0.0) 
  {
      result /= totalWeight;
  }

  return result;
}


half4 main(float2 fragCoord) 
{
   //float amount = iImage2.eval(fragCoord).a;
    float amount = sample(iImage2, fragCoord).a; 
  
   if (amount == 0.0) 
   {
       return sample(iImage1, fragCoord);
       //return iImage1.eval(fragCoord);
  }
  
  vec3 color = blur(fragCoord, direction, mix(0.1, maxSigma, amount));
  
  return half4(color, 1.0);  
}
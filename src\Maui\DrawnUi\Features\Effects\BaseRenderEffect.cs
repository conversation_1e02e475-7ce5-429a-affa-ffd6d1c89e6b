﻿namespace DrawnUi.Draw;

public class BaseChainedEffect : <PERSON>aEffect, IRenderEffect
{
    public SKPaint Paint { get; set; }

    public virtual ChainEffectResult Draw(DrawingContext ctx, Action<DrawingContext> drawControl)
    {
        return ChainEffectResult.Default;
    }

    public override void Update()
    {
        var kill = Paint;
        Paint = null;
        kill?.Dispose();

        base.Update();
    }

    protected override void OnDisposing()
    {
        Paint?.Dispose();

        base.OnDisposing();
    }
}


﻿<Project Sdk="Microsoft.NET.Sdk">

    <!--using Directory.Build.props-->

    <!--This project is going to "backlog state", will not be updated until then,
    and will be removed from main solution-->

    <!--WINDOWS-->
    <ItemGroup Condition="$(TargetFramework.Contains('windows')) == true">
        <PackageReference Include="AppoMobi.Maui.Rive" Version="*******-pre" />
    </ItemGroup>

    <PropertyGroup>
        <Title>Rive addon to DrawnUI for .NET MAUI</Title>
        <PackageId>DrawnUi.Maui.Rive</PackageId>
        <Description>SkiaRive DrawnUi control for .NET MAUI (temporarily Windows only is supported)</Description>
        <PackageTags>maui drawnui skia skiasharp draw rive</PackageTags>
        <Packable>true</Packable>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
        <CreatePackage>false</CreatePackage>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)'=='Release'">
        <WarningsAsErrors>$(WarningsAsErrors);CS0108</WarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\DrawnUi\DrawnUi.Maui.csproj" />
    </ItemGroup>


</Project>
<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Image Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Image Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="image-controls">Image Controls</h1>

<p>DrawnUi.Maui provides powerful image controls for high-performance image rendering with advanced features like effects, transformations, and sophisticated caching. This article covers the image components available in the framework.</p>
<h2 id="skiaimage">SkiaImage</h2>
<p>SkiaImage is the core image control in DrawnUi.Maui, providing efficient image loading, rendering, and manipulation capabilities with direct SkiaSharp rendering.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    Aspect=&quot;AspectFit&quot;
    HorizontalOptions=&quot;Center&quot;
    VerticalOptions=&quot;Center&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;200&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Source</code></td>
<td>ImageSource</td>
<td>Source of the image (URL, file, resource, stream)</td>
</tr>
<tr>
<td><code>Aspect</code></td>
<td>TransformAspect</td>
<td>How the image scales to fit (AspectFit, AspectFill, etc.)</td>
</tr>
<tr>
<td><code>HorizontalAlignment</code></td>
<td>DrawImageAlignment</td>
<td>Horizontal positioning of the image</td>
</tr>
<tr>
<td><code>VerticalAlignment</code></td>
<td>DrawImageAlignment</td>
<td>Vertical positioning of the image</td>
</tr>
<tr>
<td><code>RescalingQuality</code></td>
<td>SKFilterQuality</td>
<td>Quality of image rescaling</td>
</tr>
<tr>
<td><code>LoadSourceOnFirstDraw</code></td>
<td>bool</td>
<td>Whether to defer loading until first render</td>
</tr>
<tr>
<td><code>PreviewBase64</code></td>
<td>string</td>
<td>Base64 encoded preview image to show while loading</td>
</tr>
<tr>
<td><code>ImageBitmap</code></td>
<td>LoadedImageSource</td>
<td>Loaded image source (internal representation)</td>
</tr>
<tr>
<td><code>AddEffect</code></td>
<td>SkiaImageEffect</td>
<td>Built-in image effect (None, Sepia, Tint, etc.)</td>
</tr>
<tr>
<td><code>ColorTint</code></td>
<td>Color</td>
<td>Tint color for image effect</td>
</tr>
<tr>
<td><code>Brightness</code></td>
<td>double</td>
<td>Adjusts image brightness</td>
</tr>
<tr>
<td><code>Contrast</code></td>
<td>double</td>
<td>Adjusts image contrast</td>
</tr>
<tr>
<td><code>Saturation</code></td>
<td>double</td>
<td>Adjusts image saturation</td>
</tr>
<tr>
<td><code>Blur</code></td>
<td>double</td>
<td>Applies blur effect</td>
</tr>
<tr>
<td><code>Gamma</code></td>
<td>double</td>
<td>Adjusts gamma</td>
</tr>
<tr>
<td><code>Darken</code></td>
<td>double</td>
<td>Darkens the image</td>
</tr>
<tr>
<td><code>Lighten</code></td>
<td>double</td>
<td>Lightens the image</td>
</tr>
<tr>
<td><code>ZoomX</code>/<code>ZoomY</code></td>
<td>double</td>
<td>Zoom/scaling factors</td>
</tr>
<tr>
<td><code>HorizontalOffset</code>/<code>VerticalOffset</code></td>
<td>double</td>
<td>Offset for image position</td>
</tr>
<tr>
<td><code>SpriteWidth</code>/<code>SpriteHeight</code></td>
<td>double</td>
<td>Sprite sheet cell size</td>
</tr>
<tr>
<td><code>SpriteIndex</code></td>
<td>int</td>
<td>Index of sprite to display</td>
</tr>
</tbody>
</table>
<blockquote>
<p><strong>Note:</strong> The <code>UseCache</code> property is not directly on SkiaImage, but caching is handled by the SkiaControl base class or internally. You can set <code>Cache</code> on SkiaImage for caching strategies (e.g., <code>Cache=&quot;Image&quot;</code>).</p>
</blockquote>
<blockquote>
<p><strong>Note:</strong> The <code>VisualEffects</code> property is inherited from SkiaControl. You can use <code>&lt;DrawUi:SkiaControl.VisualEffects&gt;</code> in XAML to apply effects like drop shadow or color presets.</p>
</blockquote>
<h3 id="aspect-modes">Aspect Modes</h3>
<p>The <code>Aspect</code> property controls how the image is sized and positioned within its container. This is a critical property for ensuring that your images display correctly while maintaining their proportions when appropriate.</p>
<h4 id="available-aspect-modes">Available Aspect Modes</h4>
<table>
<thead>
<tr>
<th>Aspect Mode</th>
<th>Description</th>
<th>Visual Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>AspectFit</code></td>
<td>Maintains aspect ratio while ensuring the entire image fits within available space</td>
<td>May leave empty space at sides or top/bottom</td>
</tr>
<tr>
<td><code>AspectFill</code></td>
<td>Maintains aspect ratio while filling the entire space</td>
<td>May crop portions of the image that don't fit</td>
</tr>
<tr>
<td><code>Fill</code></td>
<td>Stretches the image to fill the entire space</td>
<td>May distort the image proportions</td>
</tr>
<tr>
<td><code>Center</code></td>
<td>Centers the image at its original size</td>
<td>May clip or leave empty space depending on size</td>
</tr>
<tr>
<td><code>TopLeft</code></td>
<td>Positions the image at original size in the top-left corner</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>TopCenter</code></td>
<td>Positions the image at original size at the top-center</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>TopRight</code></td>
<td>Positions the image at original size in the top-right corner</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>CenterLeft</code></td>
<td>Positions the image at original size at the center-left</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>CenterRight</code></td>
<td>Positions the image at original size at the center-right</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>BottomLeft</code></td>
<td>Positions the image at original size in the bottom-left corner</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>BottomCenter</code></td>
<td>Positions the image at original size at the bottom-center</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>BottomRight</code></td>
<td>Positions the image at original size in the bottom-right corner</td>
<td>May clip or leave empty space</td>
</tr>
<tr>
<td><code>ScaleDown</code></td>
<td>Like AspectFit, but only scales down, never up</td>
<td>Small images remain at original size</td>
</tr>
<tr>
<td><code>AspectRatioWidth</code></td>
<td>Maintains aspect ratio as determined by width</td>
<td>Sets the height based on image aspect ratio</td>
</tr>
<tr>
<td><code>AspectRatioHeight</code></td>
<td>Maintains aspect ratio as determined by height</td>
<td>Sets the width based on image aspect ratio</td>
</tr>
<tr>
<td><code>AspectCover</code></td>
<td>Same as AspectFill</td>
<td>Alternative name for AspectFill</td>
</tr>
</tbody>
</table>
<h4 id="examples-and-visual-guide">Examples and Visual Guide</h4>
<pre><code class="lang-xml">&lt;!-- Maintain aspect ratio, fit within bounds --&gt;
&lt;DrawUi:SkiaImage Source=&quot;image.png&quot; Aspect=&quot;AspectFit&quot; /&gt;
</code></pre>
<p>This ensures the entire image is visible, possibly with letterboxing (empty space) on the sides or top/bottom.</p>
<pre><code class="lang-xml">&lt;!-- Maintain aspect ratio, fill bounds (may crop) --&gt;
&lt;DrawUi:SkiaImage Source=&quot;image.png&quot; Aspect=&quot;AspectFill&quot; /&gt;
</code></pre>
<p>This fills the entire control with the image, possibly cropping parts that don't fit. Great for background images or thumbnails.</p>
<pre><code class="lang-xml">&lt;!-- Stretch to fill bounds (may distort) --&gt;
&lt;DrawUi:SkiaImage Source=&quot;image.png&quot; Aspect=&quot;Fill&quot; /&gt;
</code></pre>
<p>This stretches the image to fill the control exactly, potentially distorting the image proportions.</p>
<pre><code class="lang-xml">&lt;!-- Center the image without scaling --&gt;
&lt;DrawUi:SkiaImage Source=&quot;image.png&quot; Aspect=&quot;Center&quot; /&gt;
</code></pre>
<p>This displays the image at its original size, centered in the control. Parts may be clipped if the image is larger than the control.</p>
<h4 id="combining-aspect-and-alignment">Combining Aspect and Alignment</h4>
<p>You can combine <code>Aspect</code> with <code>HorizontalAlignment</code> and <code>VerticalAlignment</code> for precise control:</p>
<pre><code class="lang-xml">&lt;!-- AspectFit with custom alignment --&gt;
&lt;DrawUi:SkiaImage 
    Source=&quot;image.png&quot; 
    Aspect=&quot;AspectFit&quot;
    HorizontalAlignment=&quot;Start&quot;
    VerticalAlignment=&quot;End&quot; /&gt;
</code></pre>
<p>This would fit the image within bounds while aligning it to the bottom-left corner of the available space.</p>
<h4 id="choosing-the-right-aspect-mode">Choosing the Right Aspect Mode</h4>
<ul>
<li>For user photos or content images: <code>AspectFit</code> ensures the entire image is visible</li>
<li>For backgrounds or covers: <code>AspectFill</code> ensures no empty space is visible</li>
<li>For icons that need to fill a specific area: <code>Fill</code> may be appropriate</li>
<li>For pixel-perfect icons: <code>Center</code> or other position-specific modes maintain original dimensions</li>
<li>For responsive layouts: <code>AspectRatioWidth</code> or <code>AspectRatioHeight</code> can help maintain proportions while adapting to container changes</li>
</ul>
<h3 id="image-alignment">Image Alignment</h3>
<p>Control the alignment of the image within its container:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    Aspect=&quot;AspectFit&quot;
    HorizontalAlignment=&quot;End&quot;
    VerticalAlignment=&quot;Start&quot; /&gt;
</code></pre>
<p>This will position the image at the top-right of its container.</p>
<h3 id="image-effects">Image Effects</h3>
<p>SkiaImage supports various built-in effects through the <code>AddEffect</code> property:</p>
<pre><code class="lang-xml">&lt;!-- Apply a sepia effect --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    AddEffect=&quot;Sepia&quot; /&gt;

&lt;!-- Apply a tint effect --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    AddEffect=&quot;Tint&quot;
    ColorTint=&quot;Red&quot; /&gt;

&lt;!-- Apply grayscale effect --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    AddEffect=&quot;BlackAndWhite&quot; /&gt;
</code></pre>
<h3 id="image-adjustments">Image Adjustments</h3>
<p>Fine-tune image appearance with various adjustment properties:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    Brightness=&quot;1.2&quot;
    Contrast=&quot;1.1&quot;
    Saturation=&quot;0.8&quot;
    Blur=&quot;2&quot; /&gt;
</code></pre>
<h3 id="advanced-effects">Advanced Effects</h3>
<p>For more complex effects, use the VisualEffects collection:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage Source=&quot;image.png&quot;&gt;
    &lt;DrawUi:SkiaControl.VisualEffects&gt;
        &lt;DrawUi:DropShadowEffect 
            Blur=&quot;8&quot;
            X=&quot;2&quot;
            Y=&quot;2&quot;
            Color=&quot;#80000000&quot; /&gt;
        &lt;DrawUi:ChainColorPresetEffect Preset=&quot;Sepia&quot; /&gt;
    &lt;/DrawUi:SkiaControl.VisualEffects&gt;
&lt;/DrawUi:SkiaImage&gt;
</code></pre>
<h3 id="sprite-sheets">Sprite Sheets</h3>
<p>SkiaImage supports sprite sheets for displaying a single sprite from a larger image:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;sprite-sheet.png&quot;
    SpriteWidth=&quot;64&quot;
    SpriteHeight=&quot;64&quot;
    SpriteIndex=&quot;2&quot; /&gt;
</code></pre>
<p>This shows the third sprite (index 2) from the sprite sheet, assuming each sprite is 64x64 pixels.</p>
<h3 id="preview-images">Preview Images</h3>
<p>Show a low-resolution placeholder while loading the main image:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;https://example.com/large-image.jpg&quot;
    PreviewBase64=&quot;data:image/png;base64,iVBORw0KGgoAA...&quot;
    Aspect=&quot;AspectFit&quot; /&gt;
</code></pre>
<h3 id="loading-options">Loading Options</h3>
<p>Control how and when images are loaded:</p>
<pre><code class="lang-xml">&lt;!-- Immediate loading (default) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    LoadSourceOnFirstDraw=&quot;False&quot; /&gt;

&lt;!-- Deferred loading (load when first rendered) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    LoadSourceOnFirstDraw=&quot;True&quot; /&gt;
</code></pre>
<h3 id="caching-strategies">Caching Strategies</h3>
<p>Optimize performance with various caching options:</p>
<pre><code class="lang-xml">&lt;!-- Use double-buffered image caching (good for changing content) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    UseCache=&quot;ImageDoubleBuffered&quot; /&gt;

&lt;!-- Use simple image caching (good for static content) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    UseCache=&quot;Image&quot; /&gt;

&lt;!-- Cache drawing operations rather than bitmap (memory efficient) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    UseCache=&quot;Operations&quot; /&gt;

&lt;!-- No caching (for frequently changing images) --&gt;
&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    UseCache=&quot;None&quot; /&gt;
</code></pre>
<h3 id="handling-load-events">Handling Load Events</h3>
<p>You can respond to image load success or failure in code-behind:</p>
<pre><code class="lang-csharp">public MainPage()
{
    InitializeComponent();
    
    MyImage.OnSuccess += (sender, e) =&gt; {
        // Image loaded successfully
    };
    
    MyImage.OnError += (sender, e) =&gt; {
        // Image failed to load
        // e.Contains the error information
    };
}
</code></pre>
<h2 id="image-management">Image Management</h2>
<h3 id="skiaimagemanager">SkiaImageManager</h3>
<p>DrawnUi.Maui includes a powerful image management system through the <code>SkiaImageManager</code> class. This provides centralized image loading, caching, and resource management.</p>
<h4 id="preloading-images">Preloading Images</h4>
<p>Preload images to ensure they're ready when needed:</p>
<pre><code class="lang-csharp">// Preload a single image
await SkiaImageManager.Instance.PreloadImage(&quot;Images/my-image.jpg&quot;);

// Preload multiple images
await SkiaImageManager.Instance.PreloadImages(new List&lt;string&gt; 
{
    &quot;Images/image1.jpg&quot;,
    &quot;Images/image2.jpg&quot;,
    &quot;Images/image3.jpg&quot;
});
</code></pre>
<h4 id="managing-memory-usage">Managing Memory Usage</h4>
<p>Configure the image manager for optimal memory usage:</p>
<pre><code class="lang-csharp">// Enable bitmap reuse for better memory usage
SkiaImageManager.Instance.ReuseBitmaps = true;

// Set the maximum cache size (in bytes)
SkiaImageManager.Instance.MaxCacheSize = 50 * 1024 * 1024; // 50MB

// Clear unused cached images
SkiaImageManager.Instance.ClearUnusedImages();

// Clear all cached images
SkiaImageManager.Instance.ClearAll();
</code></pre>
<h2 id="advanced-usage">Advanced Usage</h2>
<h3 id="loading-from-base64">Loading from Base64</h3>
<p>Load images directly from base64 strings:</p>
<pre><code class="lang-csharp">var base64String = &quot;data:image/png;base64,iVBORw0KGgoAA...&quot;;
myImage.SetFromBase64(base64String);
</code></pre>
<h3 id="applying-transformations">Applying Transformations</h3>
<p>Apply transformations to the displayed image:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;image.png&quot;
    ZoomX=&quot;1.2&quot;
    ZoomY=&quot;1.2&quot;
    HorizontalOffset=&quot;10&quot;
    VerticalOffset=&quot;-5&quot; /&gt;
</code></pre>
<h3 id="creating-images-in-code">Creating Images in Code</h3>
<p>Create and configure SkiaImage controls programmatically:</p>
<pre><code class="lang-csharp">var image = new SkiaImage
{
    Source = &quot;Images/my-image.jpg&quot;,
    LoadSourceOnFirstDraw = false,
    Aspect = Aspect.AspectFit,
    RescalingQuality = SKFilterQuality.Medium,
    AddEffect = SkiaImageEffect.Sepia,
    WidthRequest = 200,
    HeightRequest = 200,
    HorizontalOptions = LayoutOptions.Center,
    VerticalOptions = LayoutOptions.Center
};

myLayout.Children.Add(image);
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="optimization-tips">Optimization Tips</h3>
<ol>
<li><p><strong>Image Size</strong></p>
<ul>
<li>Resize images to their display size before including in your app</li>
<li>Use compressed formats (WebP, optimized PNG/JPEG) when possible</li>
<li>Consider providing different image sizes for different screen densities</li>
</ul>
</li>
<li><p><strong>Caching</strong></p>
<ul>
<li>Use <code>UseCache=&quot;Image&quot;</code> for static images that don't change</li>
<li>Use <code>UseCache=&quot;ImageDoubleBuffered&quot;</code> for images that change occasionally</li>
<li>Use <code>UseCache=&quot;Operations&quot;</code> for images with effects but static content</li>
<li>Use <code>UseCache=&quot;None&quot;</code> only for frequently changing images</li>
</ul>
</li>
<li><p><strong>Loading Strategy</strong></p>
<ul>
<li>Use <code>LoadSourceOnFirstDraw=&quot;True&quot;</code> for off-screen images</li>
<li>Preload important images with SkiaImageManager.PreloadImages()</li>
<li>Provide preview images with <code>PreviewBase64</code> for large remote images</li>
</ul>
</li>
<li><p><strong>Rendering Quality</strong></p>
<ul>
<li>Set appropriate <code>RescalingQuality</code> based on your needs:
<ul>
<li><code>None</code>: Fastest but lowest quality</li>
<li><code>Low</code>: Good balance for scrolling content</li>
<li><code>Medium</code>: Good for static content</li>
<li><code>High</code>: Best quality but slowest (use sparingly)</li>
</ul>
</li>
</ul>
</li>
<li><p><strong>Memory Management</strong></p>
<ul>
<li>Enable bitmap reuse with <code>SkiaImageManager.Instance.ReuseBitmaps = true</code></li>
<li>Set reasonable cache limits with <code>MaxCacheSize</code></li>
<li>Call <code>ClearUnusedImages()</code> when appropriate</li>
</ul>
</li>
</ol>
<h3 id="examples-of-optimized-image-loading">Examples of Optimized Image Loading</h3>
<h4 id="for-listscarousels">For Lists/Carousels</h4>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;{Binding ImageUrl}&quot;
    LoadSourceOnFirstDraw=&quot;True&quot;
    UseCache=&quot;ImageDoubleBuffered&quot;
    RescalingQuality=&quot;Low&quot;
    Aspect=&quot;AspectFill&quot; /&gt;
</code></pre>
<h4 id="for-herocover-images">For Hero/Cover Images</h4>
<pre><code class="lang-xml">&lt;DrawUi:SkiaImage
    Source=&quot;{Binding CoverImage}&quot;
    PreviewBase64=&quot;{Binding CoverImagePreview}&quot;
    LoadSourceOnFirstDraw=&quot;False&quot;
    UseCache=&quot;Image&quot;
    RescalingQuality=&quot;Medium&quot;
    Aspect=&quot;AspectFill&quot; /&gt;
</code></pre>
<h4 id="for-image-galleries">For Image Galleries</h4>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll Orientation=&quot;Horizontal&quot;&gt;
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Row&quot; Spacing=&quot;10&quot;&gt;
        &lt;!-- Images that are initially visible --&gt;
        &lt;DrawUi:SkiaImage
            Source=&quot;{Binding Images[0]}&quot;
            LoadSourceOnFirstDraw=&quot;False&quot;
            UseCache=&quot;Image&quot;
            WidthRequest=&quot;300&quot;
            HeightRequest=&quot;200&quot; /&gt;
            
        &lt;!-- Images that may be scrolled to --&gt;
        &lt;DrawUi:SkiaImage
            Source=&quot;{Binding Images[1]}&quot;
            LoadSourceOnFirstDraw=&quot;True&quot;
            UseCache=&quot;Image&quot;
            WidthRequest=&quot;300&quot;
            HeightRequest=&quot;200&quot; /&gt;
            
        &lt;!-- More images... --&gt;
    &lt;/DrawUi:SkiaLayout&gt;
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi.Maui/blob/master/docs/articles/controls/images.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

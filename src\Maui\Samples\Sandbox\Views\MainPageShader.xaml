﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainPageShader"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:Name="ThisPage"
    x:DataType="demo:MainPageViewModel"
    BackgroundColor="White">

    <ContentPage.Resources>
        <ResourceDictionary />
    </ContentPage.Resources>

    <draw:Canvas
        x:Name="MainCanvas"
        BackgroundColor="Black"
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        HorizontalOptions="Fill"
        Tag="MainPage"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            x:Name="MainLayout"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Spacing="0"
                Tag="SkiaContainer"
                Type="Column"
                VerticalOptions="Fill">

                <!--<draw:SkiaLabel
                BackgroundColor="Red"
                HorizontalOptions="Center"
                AddMarginTop="24"
                Text="Windows and Catalyst unsupported yet, SOON!"
                TextColor="White">
                <draw:SkiaLabel.IsVisible>
                    <OnPlatform x:TypeArguments="x:Boolean">
                        <On
                            Platform="WinUI"
                            Value="True" />
                        <On
                            Platform="MacCatalyst"
                            Value="True" />
                    </OnPlatform>
                </draw:SkiaLabel.IsVisible>
            </draw:SkiaLabel>-->

                <draw:SkiaLayout
                    x:Name="StackContainer"
                    Padding="24"
                    BackgroundColor="Green"
                    Spacing="24"
                    Type="Column"
                    VerticalOptions="Start"
                    WidthRequest="300">

                    <!--  shader blur working on it.. not yet  -->
                    <!--<controls:TestShader BackgroundColor="Black">

                    <draw:SkiaImage
                        Margin="0,20"
                        Aspect="AspectCover"
                        HeightRequest="150"
                        HorizontalOptions="Center"
                        Source="Images/nico.jpg"
                        WidthRequest="250" />

                </controls:TestShader>-->

                    <draw:SkiaLabel
                        Margin="16"
                        DropShadowColor="#33000000"
                        DropShadowSize="2"
                        FontSize="14"
                        Text="PAN IMAGE WITH FINGER"
                        TextColor="White" />

                    <!--  shader curl  -->
                    <draw:SkiaLayout WidthRequest="250">

                        <draw:SkiaLabel
                            FontSize="12"
                            Text="When a texture is in an altas, it's not addressed by coordinates from (0,0) to (1,1) anymore. The atlas is really one large texture that has been assembled behind the scenes."
                            TextColor="White"
                            VerticalOptions="Center"
                            ZIndex="-1" />



                        <!--  project-specific custom control with shader  -->
                        <controls:ContentFolder SecondarySource="Images/leather.jpg" VerticalMargin="{Binding Source={x:Reference Texture}, Path=Margins.Top}">

                            <!--  this cache will be used by our folder custom control  -->
                            <draw:SkiaLayout UseCache="Image">

                                <draw:SkiaImage
                                    x:Name="Texture"
                                    Margin="0,20"
                                    Aspect="AspectCover"
                                    HeightRequest="150"
                                    HorizontalOptions="Center"
                                    Source="Images/nico.jpg"
                                    WidthRequest="250" />

                            </draw:SkiaLayout>

                        </controls:ContentFolder>
                    </draw:SkiaLayout>

                </draw:SkiaLayout>

            </draw:SkiaLayout>

            <controls:ButtonToRoot />

            <draw:SkiaLabel
                Margin="50,0"
                Padding="2"
                AddMarginBottom="80"
                BackgroundColor="#33000000"
                FontSize="16"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Text="Shader example. Pan image with your finger."
                TextColor="White"
                VerticalOptions="End" />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>



</views:BasePageCodeBehind>

﻿namespace DrawnUi.Draw
{
    public enum ShapeType
    {
        /// <summary>
        /// Default type for SkiaShape
        /// </summary>
        Rectangle,

        Circle,

        Ellipse,

        Arc,

        /// <summary>
        /// TODO unimplemented yet
        /// </summary>
        Squricle,
        /// <summary>
        /// 
        /// </summary>
        /// 
        Path,

        /// <summary>
        /// Uses multiple Points
        /// </summary>
        /// 
        Polygon,

        /// <summary>
        /// Uses multiple Points
        /// </summary>
        Line,

        /// <summary>
        /// unimplemented
        /// </summary>
        Custom
    }
}

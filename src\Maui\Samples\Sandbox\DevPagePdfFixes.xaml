<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Sandbox.DevPagePdfFixes"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    Title="ImageTestPage">

    <draw:Canvas
        BackgroundColor="Gray"
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        HorizontalOptions="Fill"
        Tag="Main"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tag="Wrapper"
            VerticalOptions="Fill">


            <draw:SkiaButton
                BackgroundColor="DodgerBlue"
                CornerRadius="12"
                FontSize="18"
                HeightRequest="50"
                HorizontalOptions="Fill"
                Text="Hello World0"
                TextColor="White"
                UseCache="Image"
                WidthRequest="-1" />

        </draw:SkiaLayout>

    </draw:Canvas>
</ContentPage>
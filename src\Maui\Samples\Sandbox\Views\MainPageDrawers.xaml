﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainPageDrawers"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:system="clr-namespace:System;assembly=System.Runtime"
    xmlns:views="clr-namespace:Sandbox.Views"
    x:Name="ThisPage"
    x:DataType="demo:MainPageViewModel">

    <draw:Canvas
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        Tag="MainPage">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tag="DrawerInside"
            VerticalOptions="Fill">


            <!--  VERTICAL  -->
            <draw:SkiaDrawer
                x:Name="Drawer"
                Direction="FromBottom"
                HeaderSize="{Binding DrawerHeaderSize}"
                HeightRequest="500"
                HorizontalOptions="Fill"
                IsOpen="{Binding IsOpen, Mode=TwoWay}"
                Tag="Drawer"
                VerticalOptions="End">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    Tag="ScrollInside"
                    VerticalOptions="Fill">

                    <!--  HEADER  -->
                    <draw:SkiaShape
                        BackgroundColor="Red"
                        CornerRadius="20,20,0,0"
                        HeightRequest="{Binding DrawerHeaderSize}"
                        HorizontalOptions="Fill"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="16,0"
                            FontSize="14"
                            HorizontalOptions="Center"
                            Text="Drag Me"
                            TextColor="White"
                            VerticalOptions="Center" />

                    </draw:SkiaShape>

                    <draw:SkiaScroll
                        AddMarginTop="{Binding DrawerHeaderSize}"
                        BackgroundColor="Gainsboro"
                        Bounces="False"
                        HorizontalOptions="Fill"
                        Tag="InsideDrawer"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            Margin="2"
                            AddMarginBottom="{Binding BottomInsets}"
                            HorizontalOptions="Fill"
                            Spacing="24"
                            Type="Column"
                            UseCache="Image">

                            <draw:SkiaLabel
                                Margin="24"
                                HorizontalOptions="Center"
                                Text="Scroll inside a drawer?" />

                            <draw:SkiaShape
                                BackgroundColor="DimGray"
                                HeightRequest="80"
                                HorizontalOptions="Center"
                                LockRatio="1"
                                Type="Circle" />

                            <draw:SkiaShape
                                BackgroundColor="Aquamarine"
                                HeightRequest="300"
                                HorizontalOptions="Center"
                                WidthRequest="50" />

                            <draw:SkiaShape
                                BackgroundColor="Goldenrod"
                                HeightRequest="80"
                                HorizontalOptions="Center"
                                LockRatio="1"
                                Type="Circle" />

                            <draw:SkiaShape
                                BackgroundColor="PeachPuff"
                                HeightRequest="300"
                                HorizontalOptions="Center"
                                WidthRequest="50" />

                            <draw:SkiaShape
                                BackgroundColor="PaleGreen"
                                HeightRequest="80"
                                HorizontalOptions="Center"
                                LockRatio="1"
                                Type="Circle" />


                        </draw:SkiaLayout>

                    </draw:SkiaScroll>

                </draw:SkiaLayout>

            </draw:SkiaDrawer>


            <!--  HORIZONTAL  -->
            <!--<draw:SkiaDrawer
                Tag="Drawer"
                HeaderSize="40"
                Direction="FromLeft"
                IsOpen="{Binding IsOpen}"
                VerticalOptions="End"
                HorizontalOptions="Start"
                BackgroundColor="Pink"
                HeightRequest="300">

                <draw:SkiaScroll
                    Orientation="Horizontal"
                    Bounces="False"
                    BackgroundColor="Gainsboro"
                    VerticalOptions="Fill"
                    Margin="0, 0,40,0">

                    <draw:SkiaLayout
                        Padding="16"
                        Spacing="24"
                        VerticalOptions="Fill"
                        Type="Row">

                        <draw:SkiaShape
                            VerticalOptions="Center"
                            BackgroundColor="DimGray"
                            LockRatio="1"
                            HeightRequest="80"
                            Type="Circle"/>

                        <draw:SkiaShape
                            BackgroundColor="Aquamarine"
                            WidthRequest="300"
                            VerticalOptions="Center"
                            HeightRequest="50"/>

                        <draw:SkiaShape
                            VerticalOptions="Center"
                            BackgroundColor="Goldenrod"
                            LockRatio="1"
                            HeightRequest="80"
                            Type="Circle"/>

                        <draw:SkiaShape
                            BackgroundColor="PeachPuff"
                            WidthRequest="300"
                            VerticalOptions="Center"
                            HeightRequest="50"/>

                        <draw:SkiaShape
                            VerticalOptions="Center"
                            BackgroundColor="PaleGreen"
                            LockRatio="1"
                            HeightRequest="80"
                            Type="Circle"/>


                    </draw:SkiaLayout>
                </draw:SkiaScroll>

            </draw:SkiaDrawer>-->

            <!--  software button  -->
            <draw:SkiaButton
                Margin="0,0,16,0"
                CommandTapped="{Binding CommandTest}"
                ControlStyle="Platform"
                HorizontalOptions="End"
                Text="Toggle"
                TranslationY="50"
                VerticalOptions="Start" />

            <draw:SkiaLabel
                Margin="16"
                BackgroundColor="Black"
                FontSize="20"
                Text="{Binding IsOpen, StringFormat='IsOpen {0}'}"
                TextColor="White" />

            <draw:SkiaLabel
                Margin="16"
                BackgroundColor="Black"
                FontSize="20"
                Text="{Binding Source={x:Reference Drawer}, Path=InTransition, StringFormat='InTransition {0}'}"
                TextColor="White"
                TranslationY="80" />



            <controls:ButtonToRoot />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>

﻿uniform float4 iMouse;            
uniform float  iTime;             
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution;  
uniform shader iImage1;  // Texture
uniform shader iImage2;   
uniform float2 iOffset;  // Top-left corner of DrawingRect
uniform float2 iOrigin;  
uniform float4 iMargins;  


//todo pass as parameters
const vec2 direction = vec2(0, 1);  
const float maxSigma = 10;
const float k = 3;
const float windowSize = k * maxSigma;
const float halfWindowSize = windowSize / 2.0;
 
const float  PI = 3.14159265359;
const vec4 backgroundColor = vec4(0.0, 0.0, 0.0, 0.0);


// Function to calculate Gaussian weight
float Gaussian(float x, float sigma) {
  return exp(-(x * x) / (2.0 * sigma * sigma)) / (2.0 * 3.14159 * sigma * sigma);
}

 half4 texture(float2 coords){

	vec2 pixelCoord = coords * iImageResolution.xy;
    return sample(iImage1, pixelCoord);
}

// Function to perform blur in one direction
vec3 blur(vec2 uv, vec2 direction, float sigma) 
{
  vec3 result = vec3(0.0, 0.0, 0.0);
  float totalWeight = 0.0;
  float window = sigma * k * 0.5;

  for (float i = -halfWindowSize; i <= halfWindowSize; i++) {
      if (abs(i) > window) {
        continue;
      }
      float weight = Gaussian(i, sigma);
      vec2 offset = vec2(direction * i);
      //vec3 sample = iImage1.eval(uv + offset).rgb;
      vec3 color = sample(iImage1, uv + offset).rgb;

      result += color * weight;
      totalWeight += weight;
  }

  if (totalWeight > 0.0) 
  {
      result /= totalWeight;
  }

  return result;
}

half4 main(float2 fragCoord) 
{	
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
	 float2 inputCoord = (fragCoord - iOffset) * renderingScale;

     vec3 color = blur(inputCoord, direction, mix(0.1, maxSigma, 1));

     //vec3 color = sample(iImage1, inputCoord).rgb;

      return half4(color, 1.0);  

      //vec3 mock =  sample(iImage1, fragCoord).rgb;
      //return half4(mock, 1.0);  
}

 
﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.Views.MainDrawnCells"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:demo="clr-namespace:Sandbox"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:controls="clr-namespace:Sandbox.Views.Controls"
    xmlns:views="clr-namespace:Sandbox.Views"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.Controls"
    x:Name="ThisPage"
    x:DataType="demo:MockChat2ViewModel"
    BackgroundColor="#333333">

    <ContentPage.Resources>
        <ResourceDictionary>

            <Style
                x:Key="TestStyle"
                TargetType="draw:SkiaLabel">
                <Setter Property="VerticalOptions" Value="Center" />
            </Style>

        </ResourceDictionary>
    </ContentPage.Resources>

    <draw:Canvas
        Margin="0,32,0,0"
        x:DataType="demo:MockChatViewModel"
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        HeightRequest="500"
        HorizontalOptions="Fill"
        VerticalOptions="Start">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaScroll
                x:Name="MainScroll"
                BackgroundColor="Black"
                Bounces="True"
                ChangeDistancePanned="1.75"
                FrictionScrolled="0.4"
                HorizontalOptions="Center"
                Scrolled="OnScrolled"
                VerticalOptions="Fill"
                WidthRequest="300">

                <!--  for dynamic height cells: ItemSizingStrategy="MeasureAllItems"  -->


                <!--
                    idea is to have a maximum number of non recycled cells, like 100.
                    so the cells pool size is 100. when we reach some border, before loadMore or start of page
                    be return 50 cells from the other direction to pool and reuse them all 50 for the up coming page
                    this way we have a lag spike on borders only but smooth scrolling through the existing content
                
                    todo this idea remains to be implemented at my free time :)
                
                    at the moment the code belows works just like maui bindable.layout + stacklayout what is not so cool
                    as we need less memory consumption
                
                    another method i already verified that works smoothly is to take maui collectioview and use canvases as cells
                -->

                <draw:SkiaLayout
                    x:Name="StackCells"
                    BackgroundColor="White"
                    HorizontalOptions="Fill"
                  
                    MeasureItemsStrategy="MeasureAll"
                    ItemsSource="{Binding Items}"
                    RecyclingTemplate="Enabled"
                    Type="Column">

                    <draw:SkiaLayout.ItemTemplate>
                        <DataTemplate x:DataType="demo:ChatMessage">

                            <!--<draw:SkiaLabel
                                Margin="8"
                                Padding="8,4"
                                BackgroundColor="#8D5BBC"
                                FontSize="14"
                                Tag="LabelText"
                                Text="{Binding Text}"
                                TextColor="White"
                                UseCache="Operations" />-->

                            <controls1:WidgetChatMessage
                                HorizontalOptions="Fill"
                                VerticalOptions="Start" />

                        </DataTemplate>

                    </draw:SkiaLayout.ItemTemplate>

                </draw:SkiaLayout>

            </draw:SkiaScroll>

            <controls:ButtonToRoot/>
            
            <draw:SkiaLabel
                Margin="8"
                BackgroundColor="Black"
                HorizontalOptions="Start"
                InputTransparent="True"
                Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
                TextColor="Lime"
                VerticalOptions="End" />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>







    </draw:Canvas>

</views:BasePageCodeBehind>

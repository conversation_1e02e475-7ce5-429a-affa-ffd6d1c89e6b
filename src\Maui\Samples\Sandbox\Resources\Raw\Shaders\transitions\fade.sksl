﻿
//vec4 getFromColor(vec2 uv){
//    return iImage1.eval(uv);
//}

//vec4 getToColor(vec2 uv){
//    return iImage2.eval(uv);
//}

vec4 transition (vec2 uv) {
  return mix(
    getFromColor(uv),
    getToColor(uv),
    progress
  );
}

half4 main(float2 fragCoord) {
    // Normalize the coordinates
    float2 normCoord = (fragCoord - iOffset) / iResolution;
    normCoord.y = 1.0 - normCoord.y;
    half4 fragColor = transition(normCoord);
    return fragColor;
}


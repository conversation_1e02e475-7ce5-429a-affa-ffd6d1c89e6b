﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
      <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <ItemGroup>
      <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.30" />
      <PackageReference Include="CommunityToolkit.Maui.Markup" Version="4.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="xunit" Version="2.5.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>    
      <PackageReference Include="Microsoft.Maui.Controls" Version="8.0.30" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Maui\DrawnUi\DrawnUi.Maui.csproj" />
  </ItemGroup>


</Project>

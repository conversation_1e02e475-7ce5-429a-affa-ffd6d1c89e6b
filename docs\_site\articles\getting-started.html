<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Getting Started with DrawnUi | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Getting Started with DrawnUi | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="getting-started-with-drawnui">Getting Started with DrawnUi</h1>

<p>This guide will help you get started with DrawnUi in your .NET MAUI application.</p>
<h2 id="installation">Installation</h2>
<h3 id="1-add-the-nuget-package">1. Add the NuGet Package</h3>
<p>Install the DrawnUi NuGet package in your .NET MAUI project:</p>
<pre><code class="lang-bash">dotnet add package AppoMobi.Maui.DrawnUi
</code></pre>
<p>You might need at least the following maui versions inside your csproj:</p>
<pre><code>    &lt;ItemGroup&gt;
        &lt;PackageReference Include=&quot;Microsoft.Maui.Controls&quot; Version=&quot;9.0.30&quot; /&gt;
        &lt;PackageReference Include=&quot;Microsoft.Maui.Controls.Compatibility&quot; Version=&quot;9.0.30&quot; /&gt;
    &lt;/ItemGroup&gt;
</code></pre>
<h3 id="2-initialize-in-your-maui-app">2. Initialize in Your MAUI App</h3>
<p>Update your <code>MauiProgram.cs</code> file to initialize draw:</p>
<pre><code class="lang-csharp">using DrawnUi.Draw;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp&lt;App&gt;()
            .UseDrawnUi() // &lt;---- Add this line
            .ConfigureFonts(fonts =&gt;
            {
                fonts.AddFont(&quot;OpenSans-Regular.ttf&quot;, &quot;OpenSansRegular&quot;);
                fonts.AddFont(&quot;OpenSans-Semibold.ttf&quot;, &quot;OpenSansSemibold&quot;);
            });

        return builder.Build();
    }
}
</code></pre>
<h3 id="add-namespace-to-xaml">Add Namespace to XAML</h3>
<p>Add the DrawnUi namespace to your XAML files:</p>
<pre><code class="lang-xml">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
             x:Class=&quot;YourNamespace.YourPage&quot;&gt;
    &lt;!-- Page content --&gt;
&lt;/ContentPage&gt;
</code></pre>
<h3 id="using-drawnui-controls">using DrawnUi Controls</h3>
<p>Now you can add DrawnUi controls to your page:</p>
<pre><code class="lang-xml">&lt;draw:DrawnUiBasePage&gt;
    &lt;draw:SkiaLayout&gt;
        &lt;draw:SkiaLabel 
            Text=&quot;Hello DrawnUi!&quot; 
            FontSize=&quot;24&quot;
            HorizontalOptions=&quot;Center&quot;
            VerticalOptions=&quot;Center&quot; /&gt;
            
        &lt;draw:SkiaButton
            Text=&quot;Click Me&quot;
            WidthRequest=&quot;120&quot;
            HeightRequest=&quot;40&quot;
            CornerRadius=&quot;8&quot;
            BackgroundColor=&quot;Blue&quot;
            TextColor=&quot;White&quot;
            VerticalOptions=&quot;Center&quot;
            HorizontalOptions=&quot;Center&quot;
            Margin=&quot;0,50,0,0&quot;
            Clicked=&quot;OnButtonClicked&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:DrawnUiBasePage&gt;
</code></pre>
<h3 id="handling-events">Handling Events</h3>
<p>Handle control events in your code-behind:</p>
<pre><code class="lang-csharp">private void OnButtonClicked(object sender, SkiaGesturesParameters e)
{
    // Handle button click
}
</code></pre>
<h2 id="using-platform-specific-styles">Using Platform-Specific Styles</h2>
<p>DrawnUi controls support platform-specific styling:</p>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Platform Style&quot;
    ControlStyle=&quot;Platform&quot;
    WidthRequest=&quot;150&quot;
    HeightRequest=&quot;40&quot; /&gt;
    
&lt;draw:SkiaSwitch
    ControlStyle=&quot;Platform&quot;
    IsToggled=&quot;true&quot;
    Margin=&quot;0,20,0,0&quot; /&gt;
</code></pre>
<h2 id="next-steps">Next Steps</h2>
<ul>
<li>Explore the <a href="controls/index.html">Controls documentation</a> to learn about available controls</li>
<li>See <a href="advanced/platform-styling.html">Platform-Specific Styling</a> for more styling options</li>
<li>Check out the <a href="samples.html">Sample Applications</a> for complete examples</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi.Maui/blob/master/docs/articles/getting-started.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

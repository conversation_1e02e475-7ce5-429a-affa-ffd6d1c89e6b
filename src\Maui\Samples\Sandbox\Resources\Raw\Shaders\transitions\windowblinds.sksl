﻿
// Author: <PERSON><PERSON><PERSON>
// License: MIT

vec4 transition (vec2 uv) {
  float t = progress;
  
  if (mod(floor(uv.y*100.*progress),2.)==0.)
    t*=2.-.5;
  
  return mix(
    getFromColor(uv),
    getToColor(uv),
    mix(t, progress, smoothstep(0.8, 1.0, progress))
  );
}

half4 main(float2 fragCoord) {
    // Normalize the coordinates
    float2 normCoord = (fragCoord - iOffset) / iResolution;
    normCoord.y = 1.0 - normCoord.y;
    half4 fragColor = transition(normCoord);
    return fragColor;
}

namespace DrawnUi.Draw;

/// <summary>
/// These are platform-independent. They correspond to JavaScript keys.
/// </summary>
public enum MauiKey
{
    Unknown,
    Backspace,
    Tab,
    Enter,
    ShiftLeft,
    ShiftRight,
    ControlLeft,
    ControlRight,
    AltLeft,
    AltRight,
    Pause,
    CapsLock,
    Escape,
    Space,
    PageUp,
    PageDown,
    End,
    Home,
    IntBackslash,
    ArrowLeft,
    ArrowUp,
    ArrowRight,
    ArrowDown,
    PrintScreen,
    Insert,
    Delete,
    Digit0,
    Digit1,
    Digit2,
    Digit3,
    Digit4,
    Digit5,
    Digit6,
    Digit7,
    Digit8,
    Digit9,
    KeyA,
    KeyB,
    KeyC,
    KeyD,
    KeyE,
    KeyF,
    KeyG,
    KeyH,
    KeyI,
    KeyJ,
    KeyK,
    KeyL,
    KeyM,
    KeyN,
    KeyO,
    KeyP,
    KeyQ,
    KeyR,
    KeyS,
    KeyT,
    KeyU,
    KeyV,
    KeyW,
    KeyX,
    KeyY,
    KeyZ,
    MetaLeft,
    MetaRight,
    ContextMenu,
    Numpad0,
    Numpad1,
    <PERSON>umpad2,
    <PERSON>umpad3,
    <PERSON><PERSON><PERSON>4,
    <PERSON>umpad5,
    <PERSON>umpad6,
    <PERSON><PERSON>ad7,
    <PERSON>ump<PERSON>8,
    <PERSON>umpad9,
    <PERSON><PERSON><PERSON><PERSON>ult<PERSON>ly,
    <PERSON>umpadAdd,
    <PERSON><PERSON>adSub<PERSON>,
    NumpadDecimal,
    NumpadDivide,
    F1,
    F2,
    F3,
    F4,
    F5,
    F6,
    F7,
    F8,
    F9,
    F10,
    F11,
    F12,
    NumLock,
    ScrollLock,
    AudioVolumeMute,
    AudioVolumeDown,
    AudioVolumeUp,
    LaunchMediaPlayer,
    LaunchApplication1,
    LaunchApplication2,
    Semicolon,
    Equal,
    Comma,
    Minus,
    Period,
    Slash,
    Backquote,
    BracketLeft,
    Backslash,
    BracketRight,
    Quote,
}

public static class MauiKeyMapper
{
    public static MauiKey MapToJava(int code)
    {
        if (KeyValues.TryGetValue(code, out var mapped))
        {
            return mapped;
        }
        return MauiKey.Unknown;
    }

    public static int MapToCode(MauiKey key)
    {
        if (KeyCodes.TryGetValue(key, out var mapped))
        {
            return mapped;
        }
        return -1;
    }

    static Dictionary<string, int> KeyCodes2 = new()
        {
            { "Backspace", 8 },
            { "Tab", 9 },
            { "Enter", 13 },
            { "ShiftLeft", 16 },
            { "ShiftRight", 16 },
            { "ControlLeft", 17 },
            { "ControlRight", 17 },
            { "AltLeft", 18 },
            { "AltRight", 18 },
            { "Pause", 19 },
            { "CapsLock", 20 },
            { "Escape", 27 },
            { "Space", 32 },
            { "PageUp", 33 },
            { "PageDown", 34 },
            { "End", 35 },
            { "Home", 36 },
            { "ArrowLeft", 37 },
            { "ArrowUp", 38 },
            { "ArrowRight", 39 },
            { "ArrowDown", 40 },
            { "PrintScreen", 44 },
            { "Insert", 45 },
            { "Delete", 46 },
            { "Digit0", 48 },
            { "Digit1", 49 },
            { "Digit2", 50 },
            { "Digit3", 51 },
            { "Digit4", 52 },
            { "Digit5", 53 },
            { "Digit6", 54 },
            { "Digit7", 55 },
            { "Digit8", 56 },
            { "Digit9", 57 },
            { "KeyA", 65 },
            { "KeyB", 66 },
            { "KeyC", 67 },
            { "KeyD", 68 },
            { "KeyE", 69 },
            { "KeyF", 70 },
            { "KeyG", 71 },
            { "KeyH", 72 },
            { "KeyI", 73 },
            { "KeyJ", 74 },
            { "KeyK", 75 },
            { "KeyL", 76 },
            { "KeyM", 77 },
            { "KeyN", 78 },
            { "KeyO", 79 },
            { "KeyP", 80 },
            { "KeyQ", 81 },
            { "KeyR", 82 },
            { "KeyS", 83 },
            { "KeyT", 84 },
            { "KeyU", 85 },
            { "KeyV", 86 },
            { "KeyW", 87 },
            { "KeyX", 88 },
            { "KeyY", 89 },
            { "KeyZ", 90 },
            { "MetaLeft", 91 },
            { "MetaRight", 92 },
            { "ContextMenu", 93 },
            { "Numpad0", 96 },
            { "Numpad1", 97 },
            { "Numpad2", 98 },
            { "Numpad3", 99 },
            { "Numpad4", 100 },
            { "Numpad5", 101 },
            { "Numpad6", 102 },
            { "Numpad7", 103 },
            { "Numpad8", 104 },
            { "Numpad9", 105 },
            { "NumpadMultiply", 106 },
            { "NumpadAdd", 107 },
            { "NumpadSubtract", 109 },
            { "NumpadDecimal", 110 },
            { "NumpadDivide", 111 },
            { "F1", 112 },
            { "F2", 113 },
            { "F3", 114 },
            { "F4", 115 },
            { "F5", 116 },
            { "F6", 117 },
            { "F7", 118 },
            { "F8", 119 },
            { "F9", 120 },
            { "F10", 121 },
            { "F11", 122 },
            { "F12", 123 },
            { "NumLock", 144 },
            { "ScrollLock", 145 },
            { "AudioVolumeMute", 173 },
            { "AudioVolumeDown", 174 },
            { "AudioVolumeUp", 175 },
            { "LaunchMediaPlayer", 181 },
            { "LaunchApplication1", 182 },
            { "LaunchApplication2", 183 },
            { "Semicolon", 186 },
            { "Equal", 187 },
            { "Comma", 188 },
            { "Minus", 189 },
            { "Period", 190 },
            { "Slash", 191 },
            { "Backquote", 192 },
            { "IntBackslash", 192 },            
            { "BracketLeft", 219 },
            { "Backslash", 220 },
            { "BracketRight", 221 },
            { "Quote", 222 },
        };

    static Dictionary<MauiKey, int> KeyCodes = new()
    {
    { MauiKey.Backspace, 8 },
    { MauiKey.Tab, 9 },
    { MauiKey.Enter, 13 },
    { MauiKey.ShiftLeft, 16 },
    { MauiKey.ShiftRight, 16 },
    { MauiKey.ControlLeft, 17 },
    { MauiKey.ControlRight, 17 },
    { MauiKey.AltLeft, 18 },
    { MauiKey.AltRight, 18 },
    { MauiKey.Pause, 19 },
    { MauiKey.CapsLock, 20 },
    { MauiKey.Escape, 27 },
    { MauiKey.Space, 32 },
    { MauiKey.PageUp, 33 },
    { MauiKey.PageDown, 34 },
    { MauiKey.End, 35 },
    { MauiKey.Home, 36 },
    { MauiKey.ArrowLeft, 37 },
    { MauiKey.ArrowUp, 38 },
    { MauiKey.ArrowRight, 39 },
    { MauiKey.ArrowDown, 40 },
    { MauiKey.PrintScreen, 44 },
    { MauiKey.Insert, 45 },
    { MauiKey.Delete, 46 },
    { MauiKey.Digit0, 48 },
    { MauiKey.Digit1, 49 },
    { MauiKey.Digit2, 50 },
    { MauiKey.Digit3, 51 },
    { MauiKey.Digit4, 52 },
    { MauiKey.Digit5, 53 },
    { MauiKey.Digit6, 54 },
    { MauiKey.Digit7, 55 },
    { MauiKey.Digit8, 56 },
    { MauiKey.Digit9, 57 },
    { MauiKey.KeyA, 65 },
    { MauiKey.KeyB, 66 },
    { MauiKey.KeyC, 67 },
    { MauiKey.KeyD, 68 },
    { MauiKey.KeyE, 69 },
    { MauiKey.KeyF, 70 },
    { MauiKey.KeyG, 71 },
    { MauiKey.KeyH, 72 },
    { MauiKey.KeyI, 73 },
    { MauiKey.KeyJ, 74 },
    { MauiKey.KeyK, 75 },
    { MauiKey.KeyL, 76 },
    { MauiKey.KeyM, 77 },
    { MauiKey.KeyN, 78 },
    { MauiKey.KeyO, 79 },
    { MauiKey.KeyP, 80 },
    { MauiKey.KeyQ, 81 },
    { MauiKey.KeyR, 82 },
    { MauiKey.KeyS, 83 },
    { MauiKey.KeyT, 84 },
    { MauiKey.KeyU, 85 },
    { MauiKey.KeyV, 86 },
    { MauiKey.KeyW, 87 },
    { MauiKey.KeyX, 88 },
    { MauiKey.KeyY, 89 },
    { MauiKey.KeyZ, 90 },
    { MauiKey.MetaLeft, 91 },
    { MauiKey.MetaRight, 92 },
    { MauiKey.ContextMenu, 93 },
    { MauiKey.Numpad0, 96 },
    { MauiKey.Numpad1, 97 },
    { MauiKey.Numpad2, 98 },
    { MauiKey.Numpad3, 99 },
    { MauiKey.Numpad4, 100 },
    { MauiKey.Numpad5, 101 },
    { MauiKey.Numpad6, 102 },
    { MauiKey.Numpad7, 103 },
    { MauiKey.Numpad8, 104 },
    { MauiKey.Numpad9, 105 },
    { MauiKey.NumpadMultiply, 106 },
    { MauiKey.NumpadAdd, 107 },
    { MauiKey.NumpadSubtract, 109 },
    { MauiKey.NumpadDecimal, 110 },
    { MauiKey.NumpadDivide, 111 },
    { MauiKey.F1, 112 },
    { MauiKey.F2, 113 },
    { MauiKey.F3, 114 },
    { MauiKey.F4, 115 },
    { MauiKey.F5, 116 },
    { MauiKey.F6, 117 },
    { MauiKey.F7, 118 },
    { MauiKey.F8, 119 },
    { MauiKey.F9, 120 },
    { MauiKey.F10, 121 },
    { MauiKey.F11, 122 },
    { MauiKey.F12, 123 },
    { MauiKey.NumLock, 144 },
    { MauiKey.ScrollLock, 145 },
    { MauiKey.AudioVolumeMute, 173 },
    { MauiKey.AudioVolumeDown, 174 },
    { MauiKey.AudioVolumeUp, 175 },
    { MauiKey.LaunchMediaPlayer, 181 },
    { MauiKey.LaunchApplication1, 182 },
    { MauiKey.LaunchApplication2, 183 },
    { MauiKey.Semicolon, 186 },
    { MauiKey.Equal, 187 },
    { MauiKey.Comma, 188 },
    { MauiKey.Minus, 189 },
    { MauiKey.Period, 190 },
    { MauiKey.Slash, 191 },
    { MauiKey.Backquote, 192 },
    { MauiKey.BracketLeft, 219 },
    { MauiKey.Backslash, 220 },
    { MauiKey.BracketRight, 221 },
    { MauiKey.Quote, 222 },
    };

    static Dictionary<int, MauiKey> KeyValues = new()
    {
        { 8, MauiKey.Backspace },
        { 9, MauiKey.Tab },
        { 13, MauiKey.Enter },
        { 16, MauiKey.ShiftLeft },  // or JavaKeyCode.ShiftRight
        { 17, MauiKey.ControlLeft },  // or JavaKeyCode.ControlRight
        { 18, MauiKey.AltLeft },  // or JavaKeyCode.AltRight
        { 19, MauiKey.Pause },
        { 20, MauiKey.CapsLock },
        { 27, MauiKey.Escape },
        { 32, MauiKey.Space },
        { 33, MauiKey.PageUp },
        { 34, MauiKey.PageDown },
        { 35, MauiKey.End },
        { 36, MauiKey.Home },
        { 37, MauiKey.ArrowLeft },
        { 38, MauiKey.ArrowUp },
        { 39, MauiKey.ArrowRight },
        { 40, MauiKey.ArrowDown },
        { 44, MauiKey.PrintScreen },
        { 45, MauiKey.Insert },
        { 46, MauiKey.Delete },
        { 48, MauiKey.Digit0 },
        { 49, MauiKey.Digit1 },
        { 50, MauiKey.Digit2 },
        { 51, MauiKey.Digit3 },
        { 52, MauiKey.Digit4 },
        { 53, MauiKey.Digit5 },
        { 54, MauiKey.Digit6 },
        { 55, MauiKey.Digit7 },
        { 56, MauiKey.Digit8 },
        { 57, MauiKey.Digit9 },
        { 65, MauiKey.KeyA },
        { 66, MauiKey.KeyB },
        { 67, MauiKey.KeyC },
        { 68, MauiKey.KeyD },
        { 69, MauiKey.KeyE },
        { 70, MauiKey.KeyF },
        { 71, MauiKey.KeyG },
        { 72, MauiKey.KeyH },
        { 73, MauiKey.KeyI },
        { 74, MauiKey.KeyJ },
        { 75, MauiKey.KeyK },
        { 76, MauiKey.KeyL },
        { 77, MauiKey.KeyM },
        { 78, MauiKey.KeyN },
        { 79, MauiKey.KeyO },
        { 80, MauiKey.KeyP },
        { 81, MauiKey.KeyQ },
        { 82, MauiKey.KeyR },
        { 83, MauiKey.KeyS },
        { 84, MauiKey.KeyT },
        { 85, MauiKey.KeyU },
        { 86, MauiKey.KeyV },
        { 87, MauiKey.KeyW },
        { 88, MauiKey.KeyX },
        { 89, MauiKey.KeyY },
        { 90, MauiKey.KeyZ },
        { 91, MauiKey.MetaLeft },
        { 92, MauiKey.MetaRight },
        { 93, MauiKey.ContextMenu },
        { 96, MauiKey.Numpad0 },
        { 97, MauiKey.Numpad1 },
        { 98, MauiKey.Numpad2 },
        { 99, MauiKey.Numpad3 },
        { 100, MauiKey.Numpad4 },
        { 101, MauiKey.Numpad5 },
        { 102, MauiKey.Numpad6 },
        { 103, MauiKey.Numpad7 },
        { 104, MauiKey.Numpad8 },
        { 105, MauiKey.Numpad9 },
        { 106, MauiKey.NumpadMultiply },
        { 107, MauiKey.NumpadAdd },
        { 109, MauiKey.NumpadSubtract },
        { 110, MauiKey.NumpadDecimal },
        { 111, MauiKey.NumpadDivide },
        { 112, MauiKey.F1 },
        { 113, MauiKey.F2 },
        { 114, MauiKey.F3 },
        { 115, MauiKey.F4 },
        { 116, MauiKey.F5 },
        { 117, MauiKey.F6 },
        { 118, MauiKey.F7 },
        { 119, MauiKey.F8 },
        { 120, MauiKey.F9 },
        { 121, MauiKey.F10 },
        { 122, MauiKey.F11 },
        { 123, MauiKey.F12 },
        { 144, MauiKey.NumLock },
        { 145, MauiKey.ScrollLock },
        { 173, MauiKey.AudioVolumeMute },
        { 174, MauiKey.AudioVolumeDown },
        { 175, MauiKey.AudioVolumeUp },
        { 181, MauiKey.LaunchMediaPlayer },
        { 182, MauiKey.LaunchApplication1 },
        { 183, MauiKey.LaunchApplication2 },
        { 186, MauiKey.Semicolon },
        { 187, MauiKey.Equal },
        { 188, MauiKey.Comma },
        { 189, MauiKey.Minus },
        { 190, MauiKey.Period },
        { 191, MauiKey.Slash },
        { 192, MauiKey.Backquote },
        { 219, MauiKey.BracketLeft },
        { 220, MauiKey.Backslash },
        { 221, MauiKey.BracketRight },
        { 222, MauiKey.Quote },
    };


}






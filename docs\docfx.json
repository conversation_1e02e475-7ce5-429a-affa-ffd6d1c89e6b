{"metadata": [{"src": [{"files": ["src/Engine/Maui/DrawnUi.Maui.csproj"], "src": "../", "exclude": ["**/bin/**", "**/obj/**"]}], "dest": "api", "disableGitFeatures": false, "disableDefaultFilter": false, "properties": {"TargetFramework": "net7.0"}}], "build": {"content": [{"files": ["api/**.yml", "api/index.md"]}, {"files": ["articles/**.md", "articles/**/toc.yml", "toc.yml", "*.md"]}], "resource": [{"files": ["images/**"]}], "overwrite": [{"files": ["apidoc/**.md"], "exclude": ["obj/**", "_site/**"]}], "dest": "_site", "globalMetadataFiles": [], "fileMetadataFiles": [], "template": ["default", "templates/material"], "postProcessors": [], "markdownEngineName": "markdig", "noLangKeyword": false, "keepFileLink": false, "cleanupCacheHistory": false, "disableGitFeatures": false, "globalMetadata": {"_appTitle": "DrawnUi Documentation", "_enableSearch": true, "_appLogoPath": "images/logo.png", "_appFaviconPath": "images/favicon.ico", "_disableContribution": false, "_gitContribute": {"repo": "https://github.com/taublast/DrawnUi.Maui", "branch": "master"}}}}
﻿using DrawnUi.Controls;

namespace DrawnUi.Draw
{
    public static partial class DrawnExtensions
    {
        public static void ConfigureHandlers(IMauiHandlersCollection handlers)
        {
            handlers.<PERSON>d<PERSON><PERSON><PERSON>(typeof(SkiaViewAccelerated), typeof(SKGLViewHandlerRetained));
            handlers.<PERSON>d<PERSON><PERSON><PERSON>(typeof(SkiaView), typeof(WinCanvasHandler));
            handlers.<PERSON><PERSON><PERSON><PERSON><PERSON>(typeof(MauiEntry), typeof(MauiEntryHandler));
            handlers.<PERSON><PERSON><PERSON><PERSON><PERSON>(typeof(MauiEditor), typeof(MauiEditorHandler));
        }
    }
}

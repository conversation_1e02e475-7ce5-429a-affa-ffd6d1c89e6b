<Project>

    <!--For All-->

    <PropertyGroup>
        <DefineConstants>$(DefineConstants);SKIA3</DefineConstants>
    </PropertyGroup>

    <PropertyGroup
        Condition="$(TargetFramework.Contains('windows')) == true Or $(TargetFramework.Contains('droid')) == true Or $(TargetFramework.Contains('ios')) == true Or $(TargetFramework.Contains('catalyst')) == true">
        <DefineConstants>$(DefineConstants);ONPLATFORM</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)'=='Release'">
        <NoWarn>1701;1702;XLS0505</NoWarn>
        <WarningsAsErrors>$(WarningsAsErrors);CS0108</WarningsAsErrors>
    </PropertyGroup>

</Project>
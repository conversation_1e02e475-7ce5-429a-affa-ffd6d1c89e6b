
<div id="sidetoggle">
  <div>
    <div class="sidefilter">
      <form class="toc-filter">
        <span class="glyphicon glyphicon-filter filter-icon"></span>
        <span class="glyphicon glyphicon-remove clear-icon" id="toc_filter_clear"></span>
        <input type="text" id="toc_filter_input" placeholder="Filter by title" onkeypress="if(event.keyCode==13) {return false;}">
      </form>
    </div>
    <div class="sidetoc">
      <div class="toc" id="toc">

          <ul class="nav level1">
                <li>
                    <a href="platform-styling.html" name="" title="Platform-Specific Styling">Platform-Specific Styling</a>
                </li>
                <li>
                    <a href="layout-system.html" name="" title="Layout System Architecture">Layout System Architecture</a>
                </li>
                <li>
                    <a href="performance.md" name="" title="Performance Optimization">Performance Optimization</a>
                </li>
                <li>
                    <a href="custom-controls.md" name="" title="Custom Controls">Custom Controls</a>
                </li>
                <li>
                    <a href="animation.md" name="" title="Animation">Animation</a>
                </li>
                <li>
                    <a href="layout-system.html" name="" title="Layout System">Layout System</a>
                </li>
                <li>
                    <a href="platform-styling.html" name="" title="Platform Styling">Platform Styling</a>
                </li>
                <li>
                    <a href="gradients.html" name="" title="Gradients">Gradients</a>
                </li>
                <li>
                    <a href="game-ui.html" name="" title="Game UI &amp; Interactive Games">Game UI &amp; Interactive Games</a>
                </li>
                <li>
                    <a href="skiascroll.html" name="" title="SkiaScroll &amp; Virtualization">SkiaScroll &amp; Virtualization</a>
                </li>
                <li>
                    <a href="gestures.html" name="" title="Gestures &amp; Touch Input">Gestures &amp; Touch Input</a>
                </li>
          </ul>
      </div>
    </div>
  </div>
</div>

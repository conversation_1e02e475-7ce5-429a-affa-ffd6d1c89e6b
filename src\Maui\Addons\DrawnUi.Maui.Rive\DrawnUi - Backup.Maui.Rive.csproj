﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net8.0;net8.0-android;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>          
           
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">14.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">14.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
  </PropertyGroup>

  <!--WINDOWS-->
  <ItemGroup Condition="$(TargetFramework.Contains('windows')) == true">
    <PackageReference Include="AppoMobi.Maui.Rive" Version="*******-pre" />
  </ItemGroup>
 
  <PropertyGroup>
    <UseNuget>true</UseNuget>
    <Version>*******</Version>
    <PackageReleaseNotes>Using SkiaSharp 2.xx. Checkout the DrawnUi demo project for usage example. Temporarily Windows only is supported.</PackageReleaseNotes>
    <Title>Rive addon to DrawnUI for .NET MAUI</Title>
    <PackageId>DrawnUi.Maui.Rive</PackageId>
    <Description>SkiaRive DrawnUi control for .NET MAUI (temporarily Windows only is supported)</Description>
    <PackageTags>maui drawnui skia skiasharp draw rive</PackageTags>
    <Authors>Nick Kovalsky aka AppoMobi</Authors>
    <Copyright>(c) AppoMobi, 2023 - present day</Copyright>
    <PackageProjectUrl>https://github.com/taublast/DrawnUi.Maui</PackageProjectUrl>
    <RepositoryUrl>https://github.com/taublast/DrawnUi.Maui</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <IncludeSymbols>true</IncludeSymbols>
    <Packable>true</Packable>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <CreatePackage>false</CreatePackage>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <WarningsAsErrors>$(WarningsAsErrors);CS0108</WarningsAsErrors>
  </PropertyGroup>

  <!--for development-->
  <ItemGroup Condition="'$(UseNuget)' != 'true'">
    <ProjectReference Include="..\..\..\Engine\DrawnUi.Maui.csproj" />
  </ItemGroup>

  <!--production-->
  <ItemGroup Condition="'$(UseNuget)' == 'true'">
    <PackageReference Include="AppoMobi.Maui.DrawnUi" Version="*******" />
  </ItemGroup>

 

</Project>
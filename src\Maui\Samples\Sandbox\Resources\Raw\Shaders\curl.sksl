﻿uniform float4 iMouse;           // Mouse drag pos=.xy Click pos=.zw (pixels)
uniform float  iTime;            // Shader playback time (s)
uniform float2 iResolution;      // Viewport resolution (pixels)
uniform float2 iImageResolution; // iImage1 resolution (pixels)
uniform shader iImage1;  // Texture
uniform shader iImage2;  // Texture for backside
uniform float2 iOffset;  // Top-left corner of DrawingRect
uniform float2 iOrigin;  
uniform float4 iMargins;  

const float cornerRadius = 0;

const float so = 0.25; //shadow opacity
const float r = 150.0; //150
const float scaleFactor = 0.2; //0.2

const float  PI = 3.14159265359;
const vec4 backgroundColor = vec4(0.0, 0.0, 0.0, 0.0);

mat3 translate(vec2 p) {
    return mat3(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, p.x, p.y, 1.0);
}

mat3 scale(vec2 s, vec2 p) {
    return translate(p) * mat3(s.x, 0.0, 0.0, 0.0, s.y, 0.0, 0.0, 0.0, 1.0) * translate(-p);
}

vec2 project(vec2 p, mat3 m) {
    return (inverse(m) * vec3(p, 1.0)).xy;
}

float inRect(vec2 p, vec4 rct) {

    rct.y += iMargins.y; //topMargin;
    rct.w -= iMargins.w; //bottomMargin

    bool inRct = p.x > rct.x && p.x < rct.z && p.y > rct.y && p.y < rct.w;
    if (!inRct) {
        return 0.0;
    }
    // Top left corner
    if (p.x < rct.x + cornerRadius && p.y < rct.y + cornerRadius) {
        return length(p - vec2(rct.x + cornerRadius, rct.y + cornerRadius)) < cornerRadius ? 1.0 : 0.0;
    }
    // Top right corner
    if (p.x > rct.z - cornerRadius && p.y < rct.y + cornerRadius) {
        return length(p - vec2(rct.z - cornerRadius, rct.y + cornerRadius)) < cornerRadius ? 1.0 : 0.0;
    }
    // Bottom left corner
    if (p.x < rct.x + cornerRadius && p.y > rct.w - cornerRadius) {
        return length(p - vec2(rct.x + cornerRadius, rct.w - cornerRadius)) < cornerRadius ? 1.0 : 0.0;
    }
    // Bottom right corner
    if (p.x > rct.z - cornerRadius && p.y > rct.w - cornerRadius) {
        return length(p - vec2(rct.z - cornerRadius, rct.w - cornerRadius)) < cornerRadius ? 1.0 : 0.0;
    }
    return 1.0;
}

//------------------------------------------
half4 main(float2 fragCoord) {
//------------------------------------------

    // Margins uniform (left, top, right, bottom)  x, y, z, w 
    float topMargin = iMargins.y; 
    float bottomMargin = iMargins.w;

    float2 renderingScale = iImageResolution.xy / iResolution.xy;
	 float2 inputCoord = (fragCoord - iOffset) * renderingScale;

    float offsetX = iMouse.x;  // Animation parameter

	vec4 container = vec4(0.0, 0.0, iResolution.x, iResolution.y); // Creates a rectangle covering the entire viewport
    vec2 center = iResolution.xy * 0.5;

    half4 fragColor = backgroundColor;
               
    vec2 xy = inputCoord;//fragCoord.xy;             
    float dx = offsetX;
    float x = container.z - dx;
    float d = xy.x - x;
   
    if (d > r) {
        //default empty background.. 
        fragColor = backgroundColor;
        if (inRect(xy, container) != 0) { //use margins
            fragColor.a = mix(so, 0.0, (d-r)/r); //drop shadow
        }
    }
    else
    if (d > 0.0) {
        float theta = asin(d / r);
        float d1 = theta * r;
        float d2 = (PI - theta) * r;

        vec2 s = vec2(1.0 + (1.0 - sin(3.14159265/2.0 + theta)) * 0.1);
        mat3 transform = scale(s, center);
        vec2 uv = project(xy, transform);
        vec2 p1 = vec2(x + d1, uv.y);

        s = vec2(1.1 + sin(PI / 2.0 + theta) * 0.1);
        transform = scale(s, center);
        uv = project(xy, transform);
        vec2 p2 = vec2(x + d2, uv.y);

        if (inRect(p2, container)!= 0) {

            fragColor = iImage2.eval(p2); //backside

        } else if (inRect(p1, container)!= 0) { //folded part
            //todo antialias borders!
            fragColor = iImage1.eval(p1);
            fragColor.rgb *= pow(clamp((r - d) / r, 0.0, 1.0), 0.2);
        } 
        else if (inRect(xy, container)!= 0) {   
            //folded part drops its own shadow         
            fragColor.a = mix(so, 0.0, (d-r)/r); //use default as for empty background
            //code below is for case when we do not use shadow for empty background
            //float adjustedY = xy.y - topMargin;  
            //float shadowHeight = max(0.0, min(1.0, (iResolution.y - bottomMargin - adjustedY) / (iResolution.y - topMargin - bottomMargin)));
            //fragColor = vec4(0.0, 0.0, 0.0, 0.5 * shadowHeight);  
        }    
    }
    else {
        vec2 s = vec2(1.2);
        mat3 transform = scale(s, center);
        vec2 uv = project(xy, transform);
        vec2 p = vec2(x + abs(d) + PI * r, uv.y);

        if (inRect(p, container)!= 0) {
            fragColor = iImage2.eval(p); //continue backside
        } else {
            //default not folded
            fragColor = iImage1.eval(xy);
        }
    }
           
    return fragColor;
}